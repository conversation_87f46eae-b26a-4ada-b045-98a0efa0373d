# BodyLabs Pilates Booking System

A comprehensive pilates class booking system with LINE OA authentication, Google Calendar integration, and multi-role management.

## 🚀 Features

### **User Roles & Permissions**
- **Super Admin**: Full system control across all branches
- **Branch Admin**: Branch-specific management, booking confirmations, course management
- **Trainer**: Schedule management, session confirmations, customer notes
- **Customer**: Course booking, session tracking, profile management

### **Core Functionality**
- ✅ LINE OA Authentication
- ✅ Advance booking system (no same-day bookings)
- ✅ Dual session confirmation (Front desk + Trainer)
- ✅ Google Calendar integration with Thai naming format
- ✅ Email, LINE OA, and Telegram notifications
- ✅ Course management with session transfers
- ✅ Activity logging and audit trails
- ✅ Membership levels based on spending
- ✅ Responsive Thai language interface

## 🏗️ Architecture

```
Frontend (Next.js) ←→ Backend (NestJS) ←→ MySQL Database
       ↓                    ↓
   LINE LIFF           Google Calendar
                       Email Service
                       Telegram Bot
```

## 📋 Prerequisites

- Node.js 18+
- MySQL 8.0+
- LINE Developer Account
- Google Cloud Console Account (for Calendar API)
- SMTP Email Service
- Docker & Docker Compose (for production)

## 🛠️ Development Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd bodylabs-pilates
```

### 2. Backend Setup
```bash
cd backend
npm install

# Configure environment
cp .env.example .env
# Edit .env with your configuration

# Start development server
npm run start:dev
```

### 3. Frontend Setup
```bash
cd frontend
npm install

# Configure environment
cp .env.local.example .env.local
# Edit .env.local with your configuration

# Start development server
npm run dev
```

### 4. Database Setup
```sql
CREATE DATABASE dev_bodylab_pilates_booking_platform;
```

The backend will automatically create tables on first run.

### 5. Create First Admin User

**Option A: Using Interactive Script (Recommended)**
```bash
cd backend
npm run create-first-admin
```

**Option B: Using CLI Command**
```bash
cd backend
npm run create-admin -- \
  --email <EMAIL> \
  --password your-secure-password \
  --firstName Admin \
  --lastName User \
  --role super_admin
```

### 6. Admin Login
- **URL**: http://localhost:3000/admin/login
- **Credentials**: Use the email/password you created above
- **Access**: Admin panel with full system control

## 🔧 Configuration

### LINE OA Setup
1. Create LINE Channel in LINE Developers Console
2. Create LIFF App
3. Configure webhook URL: `https://your-domain.com/api/auth/line/webhook`
4. Add credentials to environment variables

### Google Calendar API
1. Create project in Google Cloud Console
2. Enable Calendar API
3. Create OAuth 2.0 credentials
4. Add credentials to environment variables

### Email Configuration
Configure SMTP settings for notifications:
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

## 🚀 Production Deployment

### Using Docker Compose

1. **Prepare Environment**
```bash
cp .env.production .env
# Edit .env with production values
```

2. **Deploy**
```bash
docker-compose up -d
```

3. **SSL Setup** (Optional)
```bash
# Add SSL certificates to nginx/ssl/
# Uncomment HTTPS server block in nginx/nginx.conf
docker-compose restart nginx
```

### Manual Deployment

1. **Build Backend**
```bash
cd backend
npm run build
pm2 start dist/main.js --name bodylabs-backend
```

2. **Build Frontend**
```bash
cd frontend
npm run build
pm2 start npm --name bodylabs-frontend -- start
```

3. **Configure Nginx**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
    }
    
    location /api {
        proxy_pass http://localhost:3001;
    }
}
```

## 🧪 Testing

### Backend Tests
```bash
cd backend
npm run test
npm run test:e2e
npm run test:cov
```

### Frontend Tests
```bash
cd frontend
npm run test
npm run test:coverage
```

## 📱 Usage

### For Customers
1. Access via LINE OA or web interface
2. View available courses and remaining sessions
3. Book classes with preferred trainers
4. Receive confirmations via email and LINE
5. Track session history and trainer notes

### For Trainers
1. Login via LINE OA
2. View assigned sessions
3. Confirm session completion
4. Add session notes and recommendations
5. Manage availability schedule

### For Branch Admins
1. Manage branch bookings and confirmations
2. Create and modify courses
3. Transfer sessions between courses
4. View branch analytics and reports

### For Super Admins
1. Full system access across all branches
2. User role management
3. System-wide analytics
4. Branch performance monitoring

## 🔐 Security Features

- JWT-based authentication
- Role-based access control
- Rate limiting on API endpoints
- Input validation and sanitization
- SQL injection prevention
- XSS protection headers
- CORS configuration

## 📊 Monitoring

### Health Checks
- Backend: `GET /api/health`
- Frontend: `GET /health`
- Database: MySQL health check

### Logging
- Application logs via Winston
- Activity logs in database
- Error tracking and monitoring

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation wiki

## 🔄 Updates

### Version 1.0.0
- Initial release with core booking functionality
- LINE OA authentication
- Google Calendar integration
- Multi-role user management
- Email and LINE notifications

---

**Built with ❤️ for BodyLabs Pilates**
