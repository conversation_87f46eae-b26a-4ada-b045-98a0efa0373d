# 🔐 Admin Setup Guide

## Quick Start

### 1. Start the Backend
```bash
cd backend
npm run start:dev
```

### 2. Start the Frontend
```bash
cd frontend
npm run dev
```

### 3. Create First Admin User

**Interactive Method (Recommended):**
```bash
cd backend
npm run create-first-admin
```

**Test Admin Login:**
```bash
cd backend
npm run test-admin-login
```

**CLI Method (Alternative):**
```bash
cd backend
npm run create-admin -- \
  --email <EMAIL> \
  --password YourSecurePassword123 \
  --firstName Admin \
  --lastName User \
  --role super_admin
```

### 4. Login to Admin Panel
- **URL**: http://localhost:3000/admin/login
- **Use the email/password you created above**

## Admin System Overview

### Admin Roles
- **Super Admin**: Full system access, can manage all branches and create other admins
- **Branch Admin**: Branch-specific management, booking confirmations, course management

### Admin vs Customer Authentication
| Feature | Customers | Admins |
|---------|-----------|---------|
| **Login Method** | LINE OA | Email + Password |
| **Login URL** | `/` | `/admin/login` |
| **Database Table** | `users` | `admins` |
| **Token Type** | `user` JWT | `admin` JWT |

### Admin Capabilities
- ✅ Separate authentication system
- ✅ Role-based access control
- ✅ Secure password hashing (bcrypt)
- ✅ JWT token authentication
- ✅ Admin management (Super Admin only)
- ✅ Activity logging
- ✅ Branch assignment

### API Endpoints
- `POST /admin/auth/login` - Admin login
- `GET /admin/auth/profile` - Get admin profile
- `POST /admin/auth/create` - Create new admin (Super Admin only)
- `GET /admin/auth/admins` - List all admins (Super Admin only)
- `PUT /admin/auth/admins/:id` - Update admin (Super Admin only)
- `PUT /admin/auth/change-password` - Change password

## Troubleshooting

### Common Issues

**1. Database Connection Error**
- Ensure MySQL is running
- Check database credentials in `.env`
- Verify database exists

**2. Admin Creation Fails**
- Check if admin with same email already exists
- Verify password meets requirements (min 8 characters)
- Ensure database tables are created

**3. Login Issues**
- Verify admin was created successfully
- Check email/password combination
- Ensure backend is running on port 3001

### Database Schema
The admin system uses a separate `admins` table with these fields:
- `id` (UUID, Primary Key)
- `email` (Unique)
- `password` (Hashed with bcrypt)
- `firstName`
- `lastName`
- `phoneNumber` (Optional)
- `role` (super_admin | branch_admin)
- `isActive` (Boolean)
- `lastLoginAt` (DateTime)
- `branchId` (Foreign Key, Optional)

## Security Features
- 🔐 Password hashing with bcrypt (12 salt rounds)
- 🎫 JWT tokens with admin-specific payload
- 🛡️ Role-based access control
- 📝 Activity logging for all admin actions
- ✅ Input validation and sanitization
- 🚫 Separate authentication from customer system

## Production Considerations
- Use strong passwords for admin accounts
- Regularly rotate JWT secrets
- Monitor admin activity logs
- Implement password expiration policies
- Use HTTPS in production
- Consider 2FA for super admin accounts
