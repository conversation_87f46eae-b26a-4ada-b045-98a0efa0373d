# Supabase Setup Guide for BodyLab Pilates

## Step 1: Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Click "Start your project" 
3. Sign up/Login with GitHub (recommended)
4. Click "New Project"
5. Choose your organization
6. Fill in project details:
   - **Name**: `bodylab-pilates` (or your preferred name)
   - **Database Password**: Create a strong password (save this!)
   - **Region**: Choose closest to your location
7. Click "Create new project"
8. Wait for project to be ready (2-3 minutes)

## Step 2: Get Your Project Credentials

1. In your Supabase dashboard, go to **Settings** > **API**
2. Copy these values:
   - **Project URL**: `https://your-project-id.supabase.co`
   - **Anon public key**: `eyJ...` (long string starting with eyJ)

## Step 3: Update Your .env.local File

Replace the placeholder values in your `.env.local` file:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# LINE LIFF Configuration (keep existing values for now)
NEXT_PUBLIC_LINE_LIFF_ID=your-liff-id-here
LINE_CHANNEL_SECRET=your-channel-secret-here

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## Step 4: Set Up Database Schema

1. In Supabase dashboard, go to **SQL Editor**
2. Click "New query"
3. Copy and paste the entire content from `supabase/schema-clean.sql`
4. Click "Run" to execute the schema
5. You should see "Success. No rows returned" message

## Step 5: Configure Authentication

1. In Supabase dashboard, go to **Authentication** > **Settings**
2. Under **Site URL**, add: `http://localhost:3000`
3. Under **Redirect URLs**, add:
   - `http://localhost:3000/auth/callback`
   - `http://localhost:3000/admin/dashboard`
4. **Email Auth**: Should be enabled by default
5. **Confirm email**: You can disable this for development
6. Click "Save"

## Step 6: Test Connection

1. Restart your Next.js development server
2. Go to `http://localhost:3000`
3. You should see the app load without Supabase errors
4. Check browser console for any connection issues

## Troubleshooting

### Common Issues:

1. **"supabaseUrl is required" error**:
   - Make sure your `.env.local` file has the correct URL
   - Restart your development server after updating .env.local

2. **Database connection errors**:
   - Verify your project is fully initialized in Supabase dashboard
   - Check that the schema was executed successfully

3. **Authentication not working**:
   - Verify Site URL and Redirect URLs are set correctly
   - Make sure you're using the anon key, not the service role key

### Need Help?

If you encounter any issues:
1. Check the Supabase dashboard for error logs
2. Look at browser console for detailed error messages
3. Verify all environment variables are set correctly
