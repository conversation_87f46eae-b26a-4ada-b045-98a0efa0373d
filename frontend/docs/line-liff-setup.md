# LINE LIFF Setup Guide for BodyLab Pilates

## Overview
LINE LIFF (LINE Front-end Framework) allows your web app to run inside the LINE app, providing seamless authentication and access to LINE's features.

## Step 1: Create LINE Developer Account

1. Go to [LINE Developers Console](https://developers.line.biz/console/)
2. Login with your LINE account
3. Click "Create a new provider"
4. Enter provider information:
   - **Provider name**: `Body<PERSON>ab Pilates` (or your business name)
   - **Description**: `Pilates class reservation system`
5. Click "Create"

## Step 2: Create LINE Login Channel

1. In your provider dashboard, click "Create a new channel"
2. Select "LINE Login"
3. Fill in channel information:
   - **Channel name**: `BodyLab Pilates Login`
   - **Channel description**: `Authentication for BodyLab Pilates reservation system`
   - **App type**: `Web app`
   - **Email address**: Your business email
   - **Privacy policy URL**: Your privacy policy URL (optional for development)
   - **Terms of use URL**: Your terms URL (optional for development)
4. Click "Create"

## Step 3: Configure LINE Login Settings

1. In your LINE Login channel, go to **LINE Login** tab
2. Add callback URLs:
   - `http://localhost:3000/auth/callback` (for development)
   - `https://yourdomain.com/auth/callback` (for production)
3. Add your app domain:
   - `localhost:3000` (for development)
   - `yourdomain.com` (for production)

## Step 4: Create LIFF App

1. In your LINE Login channel, go to **LIFF** tab
2. Click "Add" to create a new LIFF app
3. Configure LIFF settings:
   - **LIFF app name**: `BodyLab Pilates`
   - **Size**: `Full` (recommended for reservation system)
   - **Endpoint URL**: `http://localhost:3000/customer` (for development)
   - **Scope**: Check `profile` and `openid`
   - **Bot link feature**: `Off` (unless you have a LINE bot)
4. Click "Add"
5. **Copy the LIFF ID** - you'll need this for your .env.local file

## Step 5: Get Your Channel Credentials

1. Go to **Basic settings** tab
2. Copy these values:
   - **Channel ID**: Your channel ID
   - **Channel secret**: Your channel secret (click "Show" to reveal)

## Step 6: Update Your Environment Variables

Update your `.env.local` file:

```env
# LINE LIFF Configuration
NEXT_PUBLIC_LINE_LIFF_ID=1234567890-abcdefgh
LINE_CHANNEL_SECRET=your_channel_secret_here
```

## Step 7: Test LIFF Integration

### For Development Testing:

1. **Option A: Use LINE App Simulator**
   - Go to [LIFF Inspector](https://liff-inspector.line.me/)
   - Enter your LIFF URL: `https://liff.line.me/{YOUR_LIFF_ID}`
   - This simulates the LINE app environment

2. **Option B: Use Real LINE App**
   - Add the LIFF URL to a LINE chat
   - Tap the URL in LINE app to open your LIFF app

### Testing URLs:
- **LIFF URL**: `https://liff.line.me/{YOUR_LIFF_ID}`
- **Direct URL**: `http://localhost:3000/customer`

## Step 8: Production Deployment

When deploying to production:

1. Update LIFF endpoint URL to your production domain
2. Update callback URLs in LINE Login settings
3. Update environment variables with production values
4. Test thoroughly in LINE app

## Troubleshooting

### Common Issues:

1. **"LIFF ID not found" error**:
   - Verify your LIFF ID is correct in .env.local
   - Make sure the LIFF app is properly created

2. **Authentication fails**:
   - Check callback URLs are correctly configured
   - Verify channel secret is correct

3. **LIFF doesn't load**:
   - Ensure endpoint URL matches your app URL
   - Check that your app is accessible from the internet (for production)

4. **Profile data not available**:
   - Make sure `profile` scope is enabled in LIFF settings
   - Check that user has granted permissions

### Development Tips:

- Use LIFF Inspector for quick testing
- Check browser console for LIFF-related errors
- LINE LIFF only works in HTTPS in production
- Test both in LINE app and external browser

## Security Notes

- Never expose your channel secret in client-side code
- Use HTTPS in production
- Validate user tokens on your server
- Implement proper session management

## Next Steps

After setup:
1. Test the customer login flow
2. Verify user profile data is retrieved correctly
3. Test booking functionality for LINE users
4. Set up proper error handling for LIFF-specific scenarios
