# Fix Authentication Error: Infinite Recursion in RLS Policy

## Problem
You're getting an error: `"infinite recursion detected in policy for relation \"users\""` when trying to access `/rest/v1/users`.

This happens because the Row Level Security (RLS) policies are trying to check if a user is an admin by querying the same `users` table they're protecting, creating a circular dependency.

## Solution

### Step 1: Run the Fix Script

1. Go to your Supabase dashboard
2. Navigate to **SQL Editor**
3. Copy and paste the entire content from `supabase/fix-rls-policies.sql`
4. Click **Run** to execute the fix

### Step 2: Verify the Fix

After running the fix script, test the authentication:

1. **Restart your development server**:
   ```bash
   # Stop the current server (Ctrl+C)
   npm run dev
   ```

2. **Test admin login**:
   - Go to `http://localhost:3000/admin/login`
   - Try to register a new admin account
   - Check if the error is resolved

3. **Check browser console** for any remaining errors

### What the Fix Does

1. **Removes problematic policies** that cause infinite recursion
2. **Creates a safe `is_admin()` function** that bypasses RLS for admin checks
3. **Implements new policies** that don't create circular dependencies
4. **Separates user access** from admin access properly

### Key Changes

- **User policies**: Users can only see/edit their own profile
- **Admin function**: `is_admin()` function safely checks admin status
- **Admin policies**: Admins can manage all data using the safe function
- **Public access**: Classes and schedules are readable by all authenticated users

### Testing After Fix

1. **Register new admin**:
   - Email: `<EMAIL>`
   - Password: `test123`
   - Display Name: `Test Admin`

2. **Login and verify**:
   - Should successfully create user profile
   - Should redirect to admin dashboard
   - Should show user info in dropdown

3. **Check database**:
   - User should appear in `auth.users` table
   - User profile should appear in `public.users` table
   - Role should be set to `admin`

### If Issues Persist

1. **Check Supabase logs**:
   - Go to Supabase dashboard > Logs
   - Look for any remaining policy errors

2. **Verify environment variables**:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
   ```

3. **Clear browser data**:
   - Clear cookies and local storage
   - Try in incognito/private mode

4. **Check network tab**:
   - Look for 401/403 errors
   - Verify API calls are using correct headers

### Alternative: Disable RLS Temporarily

If you need to test quickly, you can temporarily disable RLS:

```sql
-- TEMPORARY: Disable RLS for testing (NOT for production)
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.pilates_classes DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.class_schedules DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings DISABLE ROW LEVEL SECURITY;
```

**Remember to re-enable RLS before production!**

### Re-enable RLS Later

```sql
-- Re-enable RLS for production
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pilates_classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.class_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;
```

---

This fix should resolve the infinite recursion error and allow proper authentication to work!
