# Quick Start: Authentication Setup for BodyLab Pilates

## 🚀 Ready to Test Authentication?

### **Option 1: Admin Authentication (Easiest to start)**

1. **Set up Supabase** (5 minutes):
   - Follow `docs/supabase-setup.md`
   - Update your `.env.local` with Supabase credentials
   - Run the database schema

2. **Create admin account**:
   ```bash
   # Add service role key to .env.local first
   npm run create-demo-admin
   ```

3. **Test admin login**:
   - Go to `http://localhost:3000/admin/login`
   - Login with: `<EMAIL>` / `admin123`

### **Option 2: LINE LIFF Authentication (For mobile customers)**

1. **Set up LINE LIFF** (10 minutes):
   - Follow `docs/line-liff-setup.md`
   - Get your LIFF ID from LINE Developers Console
   - Update `.env.local` with LINE credentials

2. **Test LIFF integration**:
   - Go to `http://localhost:3000/customer`
   - Use LIFF Inspector: `https://liff-inspector.line.me/`
   - Enter LIFF URL: `https://liff.line.me/YOUR_LIFF_ID`

## 🔧 Current Authentication Status

### ✅ **What's Already Working:**

- **Admin Login Form**: Beautiful, responsive login/register form
- **Supabase Integration**: Database, auth, and user management
- **LINE LIFF Framework**: Ready for mobile customer authentication
- **Role-based Access**: Admin vs Customer routing
- **Multi-language Support**: Thai/English with cookie persistence
- **Session Management**: Automatic login state management

### 📋 **Demo Accounts Available:**

After running setup scripts:
- **Admin**: `<EMAIL>` / `admin123`
- **Customers**: `<EMAIL>` / `customer123`

## 🎯 **Quick Test Scenarios:**

### **Test Admin Flow:**
1. Go to `http://localhost:3000/admin`
2. Should redirect to login page
3. Register new admin or use demo account
4. Access admin dashboard with logout functionality

### **Test Customer Flow:**
1. Go to `http://localhost:3000/customer`
2. See LIFF integration status
3. Test with LIFF Inspector (if LINE is configured)
4. View mobile-optimized interface

### **Test Auto-Routing:**
1. Go to `http://localhost:3000`
2. Should auto-detect environment and redirect:
   - LIFF environment → Customer interface
   - Regular browser → Admin interface

## 🛠️ **Environment Variables Needed:**

```env
# Supabase (Required for admin auth)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# LINE LIFF (Optional - for customer auth)
NEXT_PUBLIC_LINE_LIFF_ID=your-liff-id
LINE_CHANNEL_SECRET=your-channel-secret

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 🚨 **Troubleshooting:**

### **Admin Login Issues:**
- Check Supabase credentials in `.env.local`
- Verify database schema is applied
- Check browser console for errors

### **LINE LIFF Issues:**
- Verify LIFF ID is correct
- Test with LIFF Inspector first
- Check that LIFF app is properly configured

### **General Issues:**
- Restart development server after changing `.env.local`
- Clear browser cache/cookies
- Check browser console for detailed errors

## 📱 **Production Deployment Notes:**

- **Supabase**: Update site URL and redirect URLs
- **LINE LIFF**: Update endpoint URL to production domain
- **Environment**: Use HTTPS for all production URLs
- **Testing**: Test both admin and customer flows thoroughly

## 🎉 **What's Next?**

Once authentication is working:
1. **Booking System**: Implement class booking functionality
2. **Payment Integration**: Add payment processing
3. **Notifications**: Set up email/LINE notifications
4. **Analytics**: Add booking analytics and reporting
5. **Mobile Optimization**: Fine-tune mobile experience

---

**Need help?** Check the detailed setup guides in the `docs/` folder or test with the built-in components!
