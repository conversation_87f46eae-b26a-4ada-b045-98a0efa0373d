import type { Config } from "tailwindcss";
const { heroui } = require("@heroui/react");

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["var(--font-noto-sans-thai)", "system-ui", "sans-serif"],
      },
      colors: {
        primary: {
          50: "#e6fffe",
          100: "#ccfffe",
          200: "#99fffd",
          300: "#66fffc",
          400: "#33fffb",
          500: "#5FCED3", // Primary color
          600: "#4ca5a9",
          700: "#397c7f",
          800: "#265254",
          900: "#13292a",
          950: "#0a1415",
          DEFAULT: "#5FCED3",
          foreground: "#ffffff",
        },
      },
    },
  },
  darkMode: "class",
  plugins: [
    heroui({
      themes: {
        light: {
          colors: {
            primary: {
              50: "#e6fffe",
              100: "#ccfffe",
              200: "#99fffd",
              300: "#66fffc",
              400: "#33fffb",
              500: "#5FCED3",
              600: "#4ca5a9",
              700: "#397c7f",
              800: "#265254",
              900: "#13292a",
              950: "#0a1415",
              DEFAULT: "#5FCED3",
              foreground: "#ffffff",
            },
          },
        },
        dark: {
          colors: {
            primary: {
              50: "#e6fffe",
              100: "#ccfffe",
              200: "#99fffd",
              300: "#66fffc",
              400: "#33fffb",
              500: "#5FCED3",
              600: "#4ca5a9",
              700: "#397c7f",
              800: "#265254",
              900: "#13292a",
              950: "#0a1415",
              DEFAULT: "#5FCED3",
              foreground: "#ffffff",
            },
          },
        },
      },
    }),
  ],
};

export default config;
