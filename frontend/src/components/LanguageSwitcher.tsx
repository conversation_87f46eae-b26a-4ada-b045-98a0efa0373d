'use client'

import { Button, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from "@heroui/react";
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';

export default function LanguageSwitcher() {
  const t = useTranslations('common');
  const router = useRouter();

  const changeLanguage = (locale: string) => {
    // Set cookie and refresh page
    document.cookie = `locale=${locale}; path=/; max-age=31536000`; // 1 year
    router.refresh();
  };

  return (
    <Dropdown>
      <DropdownTrigger>
        <Button variant="bordered" size="sm">
          {t('language')}
        </Button>
      </DropdownTrigger>
      <DropdownMenu aria-label="Language selection">
        <DropdownItem key="th" onClick={() => changeLanguage('th')}>
          {t('thai')}
        </DropdownItem>
        <DropdownItem key="en" onClick={() => changeLanguage('en')}>
          {t('english')}
        </DropdownItem>
      </DropdownMenu>
    </Dropdown>
  );
}
