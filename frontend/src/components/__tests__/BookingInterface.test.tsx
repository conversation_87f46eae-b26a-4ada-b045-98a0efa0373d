import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { jest } from '@jest/globals';
import BookingInterface from '../BookingInterface';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/api';

// Mock the contexts and API
jest.mock('@/contexts/AuthContext');
jest.mock('@/lib/api');

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

const mockUser = {
  id: '1',
  lineUserId: 'line123',
  displayName: 'Test User',
  email: '<EMAIL>',
  role: 'customer' as const,
  membershipLevel: 'bronze' as const,
  totalSpent: 1000,
  hasGivenConsent: true,
};

const mockBookings = [
  {
    id: 'booking-1',
    scheduledDateTime: '2024-01-15T10:00:00Z',
    durationMinutes: 60,
    status: 'scheduled' as const,
    notes: 'First session',
    frontConfirmed: false,
    trainerConfirmed: false,
    customer: mockUser,
    trainer: {
      id: 'trainer-1',
      displayName: '<PERSON> Trainer',
      role: 'trainer' as const,
    },
    course: {
      id: 'course-1',
      name: 'Basic Pilates',
      totalSessions: 10,
      usedSessions: 1,
      remainingSessions: 9,
    },
    branch: {
      id: 'branch-1',
      name: 'Main Branch',
      address: '123 Main St',
    },
  },
];

const mockCourses = [
  {
    id: 'course-1',
    name: 'Basic Pilates',
    description: 'Beginner friendly pilates course',
    price: 2000,
    totalSessions: 10,
    usedSessions: 1,
    remainingSessions: 9,
    startDate: '2024-01-01',
    expiryDate: '2024-03-01',
    status: 'active' as const,
    customer: mockUser,
    branch: {
      id: 'branch-1',
      name: 'Main Branch',
      address: '123 Main St',
    },
  },
];

describe('BookingInterface', () => {
  beforeEach(() => {
    mockUseAuth.mockReturnValue({
      user: mockUser,
      token: 'mock-token',
      loading: false,
      signOut: jest.fn(),
      refreshUser: jest.fn(),
      loginWithLine: jest.fn(),
    });

    mockApiClient.getBookings.mockResolvedValue({
      success: true,
      data: mockBookings,
    });

    mockApiClient.getCourses.mockResolvedValue({
      success: true,
      data: mockCourses,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders user information correctly', async () => {
    render(<BookingInterface />);

    await waitFor(() => {
      expect(screen.getByText('สวัสดี, Test User')).toBeInTheDocument();
      expect(screen.getByText('สมาชิกระดับ:')).toBeInTheDocument();
      expect(screen.getByText('bronze')).toBeInTheDocument();
    });
  });

  it('displays bookings correctly', async () => {
    render(<BookingInterface />);

    await waitFor(() => {
      expect(screen.getByText('Basic Pilates')).toBeInTheDocument();
      expect(screen.getByText('ครู: John Trainer')).toBeInTheDocument();
      expect(screen.getByText('สาขา: Main Branch')).toBeInTheDocument();
      expect(screen.getByText('จองแล้ว')).toBeInTheDocument();
    });
  });

  it('switches between tabs correctly', async () => {
    render(<BookingInterface />);

    await waitFor(() => {
      expect(screen.getByText('การจองของฉัน')).toBeInTheDocument();
    });

    // Click on courses tab
    fireEvent.click(screen.getByText('คอร์สของฉัน'));

    await waitFor(() => {
      expect(screen.getByText('คอร์สของฉัน')).toBeInTheDocument();
      expect(screen.getByText('เซสชันทั้งหมด:')).toBeInTheDocument();
      expect(screen.getByText('10')).toBeInTheDocument();
    });
  });

  it('handles booking cancellation', async () => {
    // Mock window.prompt
    const mockPrompt = jest.fn().mockReturnValue('Changed my mind');
    Object.defineProperty(window, 'prompt', {
      value: mockPrompt,
      writable: true,
    });

    mockApiClient.cancelBooking.mockResolvedValue({
      success: true,
      data: { ...mockBookings[0], status: 'cancelled' as const },
    });

    render(<BookingInterface />);

    await waitFor(() => {
      expect(screen.getByText('ยกเลิกการจอง')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('ยกเลิกการจอง'));

    expect(mockPrompt).toHaveBeenCalledWith('เหตุผลในการยกเลิก (ไม่บังคับ):');
    
    await waitFor(() => {
      expect(mockApiClient.cancelBooking).toHaveBeenCalledWith('booking-1', 'Changed my mind');
    });
  });

  it('displays course progress correctly', async () => {
    render(<BookingInterface />);

    // Switch to courses tab
    fireEvent.click(screen.getByText('คอร์สของฉัน'));

    await waitFor(() => {
      expect(screen.getByText('ใช้ไปแล้ว:')).toBeInTheDocument();
      expect(screen.getByText('1')).toBeInTheDocument();
      expect(screen.getByText('เหลือ:')).toBeInTheDocument();
      expect(screen.getByText('9')).toBeInTheDocument();
    });
  });

  it('shows loading state', () => {
    mockUseAuth.mockReturnValue({
      user: mockUser,
      token: 'mock-token',
      loading: true,
      signOut: jest.fn(),
      refreshUser: jest.fn(),
      loginWithLine: jest.fn(),
    });

    render(<BookingInterface />);

    expect(screen.getByText('กำลังโหลด...')).toBeInTheDocument();
  });

  it('shows login prompt when user is not authenticated', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      token: null,
      loading: false,
      signOut: jest.fn(),
      refreshUser: jest.fn(),
      loginWithLine: jest.fn(),
    });

    render(<BookingInterface />);

    expect(screen.getByText('กรุณาเข้าสู่ระบบเพื่อดูข้อมูลการจอง')).toBeInTheDocument();
  });

  it('handles API errors gracefully', async () => {
    mockApiClient.getBookings.mockRejectedValue(new Error('API Error'));
    mockApiClient.getCourses.mockRejectedValue(new Error('API Error'));

    render(<BookingInterface />);

    await waitFor(() => {
      expect(screen.getByText('API Error')).toBeInTheDocument();
      expect(screen.getByText('ลองใหม่')).toBeInTheDocument();
    });
  });
});
