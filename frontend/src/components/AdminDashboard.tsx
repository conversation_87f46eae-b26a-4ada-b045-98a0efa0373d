'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { apiClient, Booking, Course } from '@/lib/api'

interface AdminDashboardProps {
  className?: string
}

export default function AdminDashboard({ className = '' }: AdminDashboardProps) {
  const { user } = useAuth()
  const [bookings, setBookings] = useState<Booking[]>([])
  const [courses, setCourses] = useState<Course[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'bookings' | 'courses'>('bookings')

  useEffect(() => {
    if (user && (user.role === 'branch_admin' || user.role === 'super_admin')) {
      loadData()
    }
  }, [user])

  const loadData = async () => {
    try {
      setLoading(true)
      setError(null)

      const [bookingsResponse, coursesResponse] = await Promise.all([
        apiClient.getBookings(),
        apiClient.getCourses()
      ])

      if (bookingsResponse.success) {
        setBookings(bookingsResponse.data)
      }

      if (coursesResponse.success) {
        setCourses(coursesResponse.data)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data')
    } finally {
      setLoading(false)
    }
  }

  const handleConfirmSessionFront = async (bookingId: string) => {
    try {
      const response = await apiClient.confirmSessionFront(bookingId)
      if (response.success) {
        await loadData() // Refresh data
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to confirm session')
    }
  }

  const formatDateTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleString('th-TH', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800'
      case 'confirmed': return 'bg-green-100 text-green-800'
      case 'completed': return 'bg-gray-100 text-gray-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'scheduled': return 'จองแล้ว'
      case 'confirmed': return 'ยืนยันแล้ว'
      case 'completed': return 'เสร็จสิ้น'
      case 'cancelled': return 'ยกเลิก'
      default: return status
    }
  }

  if (!user || (user.role !== 'branch_admin' && user.role !== 'super_admin')) {
    return (
      <div className={`p-6 text-center ${className}`}>
        <p className="text-red-600">คุณไม่มีสิทธิ์เข้าถึงหน้านี้</p>
      </div>
    )
  }

  if (loading) {
    return (
      <div className={`p-6 text-center ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-gray-600">กำลังโหลด...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`p-6 text-center ${className}`}>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p>{error}</p>
          <button
            onClick={loadData}
            className="mt-2 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            ลองใหม่
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`max-w-6xl mx-auto p-6 ${className}`}>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          แดชบอร์ดผู้ดูแล
        </h1>
        <p className="text-gray-600">
          สวัสดี, {user.displayName} ({user.role === 'super_admin' ? 'ผู้ดูแลระบบ' : 'ผู้ดูแลสาขา'})
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('bookings')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'bookings'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            จัดการการจอง
          </button>
          <button
            onClick={() => setActiveTab('courses')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'courses'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            จัดการคอร์ส
          </button>
        </nav>
      </div>

      {/* Bookings Management Tab */}
      {activeTab === 'bookings' && (
        <div>
          <h2 className="text-xl font-semibold mb-4">จัดการการจอง</h2>
          {bookings.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-600">ไม่มีการจอง</p>
            </div>
          ) : (
            <div className="space-y-4">
              {bookings.map((booking) => (
                <div key={booking.id} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="font-semibold text-lg">{booking.course.name}</h3>
                      <p className="text-gray-600">ลูกค้า: {booking.customer.displayName}</p>
                      <p className="text-gray-600">ครู: {booking.trainer.displayName}</p>
                      <p className="text-gray-600">สาขา: {booking.branch.name}</p>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(booking.status)}`}>
                      {getStatusText(booking.status)}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-gray-600">วันที่และเวลา</p>
                      <p className="font-medium">{formatDateTime(booking.scheduledDateTime)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">ระยะเวลา</p>
                      <p className="font-medium">{booking.durationMinutes} นาที</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">การยืนยัน</p>
                      <div className="space-y-1">
                        <p className={`text-sm ${booking.frontConfirmed ? 'text-green-600' : 'text-gray-400'}`}>
                          ✓ Front: {booking.frontConfirmed ? 'ยืนยันแล้ว' : 'รอยืนยัน'}
                        </p>
                        <p className={`text-sm ${booking.trainerConfirmed ? 'text-green-600' : 'text-gray-400'}`}>
                          ✓ ครู: {booking.trainerConfirmed ? 'ยืนยันแล้ว' : 'รอยืนยัน'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {booking.notes && (
                    <div className="mb-4">
                      <p className="text-sm text-gray-600">หมายเหตุ</p>
                      <p className="text-gray-800">{booking.notes}</p>
                    </div>
                  )}

                  {booking.status === 'scheduled' && !booking.frontConfirmed && (
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleConfirmSessionFront(booking.id)}
                        className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
                      >
                        ยืนยันจาก Front
                      </button>
                    </div>
                  )}

                  {booking.status === 'completed' && booking.sessionNotes && (
                    <div className="mt-4 p-4 bg-gray-50 rounded">
                      <p className="text-sm text-gray-600 mb-2">บันทึกจากครู</p>
                      <p className="text-gray-800">{booking.sessionNotes}</p>
                      {booking.trainerRecommendations && (
                        <div className="mt-2">
                          <p className="text-sm text-gray-600">คำแนะนำ</p>
                          <p className="text-gray-800">{booking.trainerRecommendations}</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Courses Management Tab */}
      {activeTab === 'courses' && (
        <div>
          <h2 className="text-xl font-semibold mb-4">จัดการคอร์ส</h2>
          {courses.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-600">ไม่มีคอร์ส</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {courses.map((course) => (
                <div key={course.id} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                  <h3 className="font-semibold text-lg mb-2">{course.name}</h3>
                  <p className="text-gray-600 mb-2">ลูกค้า: {course.customer.displayName}</p>
                  <p className="text-gray-600 mb-4">สาขา: {course.branch.name}</p>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between">
                      <span className="text-gray-600">ราคา:</span>
                      <span className="font-medium">฿{course.price.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">เซสชันทั้งหมด:</span>
                      <span className="font-medium">{course.totalSessions}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">ใช้ไปแล้ว:</span>
                      <span className="font-medium">{course.usedSessions}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">เหลือ:</span>
                      <span className="font-medium text-blue-600">{course.remainingSessions}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">สถานะ:</span>
                      <span className={`font-medium ${course.status === 'active' ? 'text-green-600' : 'text-gray-600'}`}>
                        {course.status === 'active' ? 'ใช้งาน' : course.status}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">หมดอายุ:</span>
                      <span className="font-medium">{new Date(course.expiryDate).toLocaleDateString('th-TH')}</span>
                    </div>
                  </div>

                  <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{
                        width: `${(course.usedSessions / course.totalSessions) * 100}%`
                      }}
                    ></div>
                  </div>

                  <div className="flex space-x-2">
                    <button className="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700">
                      แก้ไข
                    </button>
                    <button className="flex-1 bg-gray-600 text-white px-3 py-2 rounded text-sm hover:bg-gray-700">
                      โอนเซสชัน
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
