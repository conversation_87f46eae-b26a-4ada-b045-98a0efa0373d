'use client'

import { <PERSON><PERSON>, <PERSON>, CardBody, Card<PERSON>ead<PERSON>, <PERSON>, Progress } from "@heroui/react";

export default function ThemeDemo() {
  return (
    <Card className="max-w-md">
      <CardHeader>
        <h3 className="text-lg font-semibold text-primary">Primary Color Theme Demo</h3>
      </CardHeader>
      <CardBody className="space-y-4">
        <div>
          <p className="text-small text-default-600 mb-2">Primary Color: #5FCED3</p>
          <div className="flex gap-2 flex-wrap">
            <Button color="primary" size="sm">Primary Solid</Button>
            <Button color="primary" variant="bordered" size="sm">Primary Bordered</Button>
            <Button color="primary" variant="light" size="sm">Primary Light</Button>
            <Button color="primary" variant="flat" size="sm">Primary Flat</Button>
          </div>
        </div>

        <div>
          <p className="text-small text-default-600 mb-2">Chips & Progress</p>
          <div className="flex gap-2 items-center mb-2">
            <Chip color="primary" size="sm">Primary Chip</Chip>
            <Chip color="primary" variant="bordered" size="sm">Bordered</Chip>
            <Chip color="primary" variant="light" size="sm">Light</Chip>
          </div>
          <Progress 
            color="primary" 
            value={75} 
            className="max-w-md"
            label="Progress Bar"
          />
        </div>

        <div>
          <p className="text-small text-default-600 mb-2">Text Colors</p>
          <div className="space-y-1">
            <p className="text-primary">Primary text color</p>
            <p className="text-primary-600">Primary 600</p>
            <p className="text-primary-700">Primary 700</p>
          </div>
        </div>

        <div>
          <p className="text-small text-default-600 mb-2">Background Colors</p>
          <div className="grid grid-cols-3 gap-2">
            <div className="bg-primary-50 p-2 rounded text-center text-xs">50</div>
            <div className="bg-primary-100 p-2 rounded text-center text-xs">100</div>
            <div className="bg-primary-200 p-2 rounded text-center text-xs">200</div>
            <div className="bg-primary-500 p-2 rounded text-center text-xs text-white">500</div>
            <div className="bg-primary-700 p-2 rounded text-center text-xs text-white">700</div>
            <div className="bg-primary-900 p-2 rounded text-center text-xs text-white">900</div>
          </div>
        </div>

        <div className="text-center">
          <p className="text-success text-small">✅ Primary color theme (#5FCED3) is working!</p>
        </div>
      </CardBody>
    </Card>
  );
}
