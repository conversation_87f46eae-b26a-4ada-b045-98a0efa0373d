'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON>ton,
  Card,
  CardBody,
  CardHeader,
  Input,
  Spinner,
  Select,
  SelectItem,
  Tabs,
  Tab
} from "@heroui/react"
import { EyeIcon, EyeSlashIcon, BuildingOfficeIcon, ShieldCheckIcon } from '@heroicons/react/24/outline'
import { useAdminAuth } from '@/contexts/AdminAuthContext'
import { apiClient, Branch } from '@/lib/api'

interface AdminLoginFormData {
  email: string
  password: string
  branchId?: string
}

export default function AdminLoginForm() {
  const { login } = useAdminAuth()

  const [loading, setLoading] = useState(false)
  const [loadingBranches, setLoadingBranches] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [loginType, setLoginType] = useState<'super_admin' | 'branch_admin'>('super_admin')
  const [branches, setBranches] = useState<Branch[]>([])
  const [formData, setFormData] = useState<AdminLoginFormData>({
    email: '',
    password: '',
    branchId: undefined
  })

  // Load branches when branch admin is selected
  useEffect(() => {
    if (loginType === 'branch_admin') {
      loadBranches()
    }
  }, [loginType])

  const loadBranches = async () => {
    try {
      setLoadingBranches(true)
      const response = await apiClient.getBranches()
      if (response.success) {
        setBranches(response.data.branches)
      }
    } catch (error) {
      console.error('Error loading branches:', error)
      setError('Failed to load branches. Please try again.')
    } finally {
      setLoadingBranches(false)
    }
  }

  const handleInputChange = (field: keyof AdminLoginFormData) => (value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    // Clear errors when user starts typing
    if (error) setError(null)
  }

  const handleLoginTypeChange = (type: 'super_admin' | 'branch_admin') => {
    setLoginType(type)
    setFormData(prev => ({
      ...prev,
      branchId: undefined
    }))
    if (error) setError(null)
  }

  const validateForm = () => {
    if (!formData.email || !formData.password) {
      setError('Email and password are required')
      return false
    }

    if (formData.password.length < 6) {
      setError('Password must be at least 6 characters')
      return false
    }

    if (loginType === 'branch_admin' && !formData.branchId) {
      setError('Please select a branch for branch admin login')
      return false
    }

    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setLoading(true)
    setError(null)

    try {
      const success = await login(formData.email, formData.password, formData.branchId)

      if (success) {
        // Redirect will be handled by AdminAuthContext based on role
        // Super admin -> /admin (super admin dashboard)
        // Branch admin -> /admin/branch (branch management dashboard)
      } else {
        setError('Invalid email or password')
      }
    } catch {
      setError('Login failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-[#5FCED3]/10 to-white">
      <Card className="w-full max-w-md shadow-xl">
        <CardHeader className="flex flex-col items-center pb-0 pt-6">
          <div className="flex items-center gap-3 mb-2">
            <img src="/images/logo.png" alt="BodyLabs" className="h-8 w-8" />
            <h1 className="text-2xl font-bold text-[#5FCED3]">Admin Login</h1>
          </div>
          <p className="text-small text-default-500 mt-2">
            BodyLabs Pilates Admin Panel
          </p>
        </CardHeader>
        <CardBody className="px-6 py-6">
          {/* Login Type Tabs */}
          <div className="mb-6">
            <Tabs
              selectedKey={loginType}
              onSelectionChange={(key) => handleLoginTypeChange(key as 'super_admin' | 'branch_admin')}
              className="w-full"
              variant="bordered"
            >
              <Tab
                key="super_admin"
                title={
                  <div className="flex items-center gap-2">
                    <ShieldCheckIcon className="w-4 h-4" />
                    <span>Super Admin</span>
                  </div>
                }
              />
              <Tab
                key="branch_admin"
                title={
                  <div className="flex items-center gap-2">
                    <BuildingOfficeIcon className="w-4 h-4" />
                    <span>Branch Admin</span>
                  </div>
                }
              />
            </Tabs>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
                {error}
              </div>
            )}

            <Input
              type="email"
              label="Email"
              placeholder="Enter your admin email"
              value={formData.email}
              onValueChange={handleInputChange('email')}
              isRequired
              variant="bordered"
              isDisabled={loading}
            />

            <Input
              type={showPassword ? "text" : "password"}
              label="Password"
              placeholder="Enter your password"
              value={formData.password}
              onValueChange={handleInputChange('password')}
              isRequired
              variant="bordered"
              isDisabled={loading}
              endContent={
                <button
                  className="focus:outline-none"
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-5 w-5 text-default-400" />
                  ) : (
                    <EyeIcon className="h-5 w-5 text-default-400" />
                  )}
                </button>
              }
            />

            {/* Branch Selection for Branch Admin */}
            {loginType === 'branch_admin' && (
              <Select
                label="Select Branch"
                placeholder="Choose your branch"
                selectedKeys={formData.branchId ? [formData.branchId] : []}
                onSelectionChange={(keys) => {
                  const selected = Array.from(keys)[0] as string;
                  handleInputChange('branchId')(selected);
                }}
                isRequired
                variant="bordered"
                isDisabled={loading || loadingBranches}
                isLoading={loadingBranches}
                startContent={<BuildingOfficeIcon className="w-4 h-4 text-default-400" />}
              >
                {branches.map((branch) => (
                  <SelectItem key={branch.id}>
                    {branch.name}
                  </SelectItem>
                ))}
              </Select>
            )}

            <Button
              type="submit"
              className="w-full bg-[#5FCED3] text-white hover:bg-[#5FCED3]/90"
              isLoading={loading || loadingBranches}
              isDisabled={loading || loadingBranches}
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <Spinner size="sm" />
                  <span>Signing in...</span>
                </div>
              ) : (
                `Login as ${loginType === 'super_admin' ? 'Super Admin' : 'Branch Admin'}`
              )}
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-xs text-default-500">
              {loginType === 'super_admin'
                ? 'Super Admin access - Full system control'
                : 'Branch Admin access - Branch-specific management'
              }
            </p>
            <p className="text-xs text-default-400 mt-1">
              Contact system administrator for account creation.
            </p>
          </div>
        </CardBody>
      </Card>
    </div>
  )
}