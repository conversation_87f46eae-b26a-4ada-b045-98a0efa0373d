'use client'

import { <PERSON><PERSON>, <PERSON>, CardBody, CardHeader, <PERSON><PERSON>, Spinner } from "@heroui/react";
// import { useTranslations } from 'next-intl';
import { useLineAuth } from '@/contexts/LineAuthContext';

export default function LineAuth() {
  // const t = useTranslations('common');
  const {
    isLiffReady,
    isLoggedIn,
    profile,
    user,
    loading,
    login,
    logout
  } = useLineAuth();

  if (loading) {
    return (
      <Card className="max-w-md">
        <CardBody className="flex items-center justify-center py-8">
          <Spinner size="lg" color="primary" />
          <p className="mt-4">Initializing LINE LIFF...</p>
        </CardBody>
      </Card>
    );
  }

  if (!isLiffReady) {
    return (
      <Card className="max-w-md">
        <CardHeader>
          <h3 className="text-lg font-semibold text-warning">LINE LIFF Not Available</h3>
        </CardHeader>
        <CardBody>
          <p className="text-default-600 mb-4">
            LINE LIFF is not properly configured or not available in this environment.
          </p>
          <p className="text-small text-default-500 mb-4">
            This feature is only available when:
            <br />• LIFF ID is configured in environment variables
            <br />• Running in LINE app or LIFF browser
            <br />• Accessing via LIFF URL
          </p>
          <div className="bg-blue-50 p-3 rounded-lg">
            <p className="text-small text-blue-800 font-medium">For Development:</p>
            <p className="text-small text-blue-600">
              1. Set up LINE LIFF following the guide in docs/line-liff-setup.md
              <br />
              2. Test using LIFF Inspector: liff-inspector.line.me
              <br />
              3. Or access via LINE app with LIFF URL
            </p>
          </div>
        </CardBody>
      </Card>
    );
  }

  if (!isLoggedIn) {
    return (
      <Card className="max-w-md">
        <CardHeader>
          <h3 className="text-lg font-semibold">LINE Login Required</h3>
        </CardHeader>
        <CardBody>
          <p className="text-default-600 mb-4">
            Please log in with your LINE account to access the reservation system.
          </p>
          <Button
            color="primary"
            variant="solid"
            onClick={login}
            className="w-full"
          >
            Login with LINE
          </Button>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card className="max-w-md">
      <CardHeader>
        <h3 className="text-lg font-semibold text-success">✅ LINE Authentication</h3>
      </CardHeader>
      <CardBody>
        {profile && (
          <div className="flex items-center gap-4 mb-4">
            <Avatar
              src={profile.pictureUrl}
              name={profile.displayName}
              size="lg"
            />
            <div>
              <p className="font-medium">{profile.displayName}</p>
              <p className="text-small text-default-500">LINE User ID: {profile.userId.slice(0, 8)}...</p>
              {user && (
                <p className="text-small text-primary">Role: {user.role}</p>
              )}
            </div>
          </div>
        )}

        <div className="space-y-2">
          <p className="text-small text-success">✅ LIFF initialized</p>
          <p className="text-small text-success">✅ User authenticated</p>
          {user && <p className="text-small text-success">✅ Database user created</p>}
        </div>

        <Button
          color="danger"
          variant="bordered"
          onClick={logout}
          className="w-full mt-4"
          size="sm"
        >
          Logout
        </Button>
      </CardBody>
    </Card>
  );
}
