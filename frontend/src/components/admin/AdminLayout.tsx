"use client";

import React, { useState } from "react";
import AdminSidebar from "./AdminSidebar";
import AdminLoading from "./AdminLoading";
import { useAdminAuth } from "@/contexts/AdminAuthContext";

interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
}

export default function AdminLayout({
  children,
  title,
  subtitle,
}: AdminLayoutProps) {
  const { admin, loading } = useAdminAuth();
  const [isCollapsed, setIsCollapsed] = useState(false);

  if (loading) {
    return (
      <AdminLoading
        message="Loading admin portal..."
        size="lg"
        fullScreen={true}
      />
    );
  }

  if (!admin) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      <AdminSidebar
        userRole={admin?.role}
        userName={admin?.fullName}
        userEmail={admin?.email}
        onCollapseChange={setIsCollapsed}
      />

      {/* Main Content */}
      <div className={`min-h-screen transition-all duration-500 ease-in-out ${isCollapsed ? 'ml-20' : 'ml-80'}`}>
        {/* Header */}
        {(title || subtitle) && (
          <div className="bg-white/80 backdrop-blur-sm border-b border-slate-200/50 px-8 py-6">
            <div className="w-full">
              {title && (
                <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-2">
                  {title}
                </h1>
              )}
              {subtitle && (
                <p className="text-slate-600 text-lg">{subtitle}</p>
              )}
            </div>
          </div>
        )}

        {/* Page Content */}
        <div className="p-8 w-full">
          {children}
        </div>
      </div>
    </div>
  );
}
