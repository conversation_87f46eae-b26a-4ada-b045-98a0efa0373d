'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, Card, CardBody, CardHeader, Chip, Code } from "@heroui/react"
import { liffService } from '@/lib/liff'

export default function LiffTester() {
  const [liffInfo, setLiffInfo] = useState<{
    isLoggedIn: boolean;
    os: string;
    version: string;
    language: string;
    isInClient: boolean;
    availableApis: Record<string, boolean>;
  } | null>(null)
  const [isReady, setIsReady] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const initLiff = async () => {
      try {
        const ready = await liffService.init()
        setIsReady(ready)

        if (ready) {
          const info = {
            isLoggedIn: liffService.isLoggedIn(),
            os: liffService.getOS(),
            version: liffService.getVersion(),
            language: liffService.getLanguage(),
            isInClient: liffService.isInClient(),
            availableApis: {
              sendMessages: liffService.isApiAvailable('sendMessages'),
              shareTargetPicker: liffService.isApiAvailable('shareTargetPicker'),
            }
          }
          setLiffInfo(info)
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
      }
    }

    initLiff()
  }, [])

  const handleLogin = async () => {
    try {
      await liffService.login()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed')
    }
  }

  const handleGetProfile = async () => {
    try {
      const profile = await liffService.getProfile()
      console.log('Profile:', profile)
      alert(`Profile: ${JSON.stringify(profile, null, 2)}`)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get profile')
    }
  }

  return (
    <Card className="max-w-2xl">
      <CardHeader>
        <h3 className="text-lg font-semibold">LINE LIFF Tester</h3>
      </CardHeader>
      <CardBody className="space-y-4">
        {error && (
          <div className="bg-danger-50 p-3 rounded-lg">
            <p className="text-danger text-small">{error}</p>
          </div>
        )}

        <div>
          <h4 className="font-medium mb-2">LIFF Status</h4>
          <div className="flex gap-2 mb-2">
            <Chip color={isReady ? "success" : "danger"} size="sm">
              {isReady ? "Ready" : "Not Ready"}
            </Chip>
            {liffInfo?.isLoggedIn && (
              <Chip color="primary" size="sm">Logged In</Chip>
            )}
          </div>
        </div>

        {liffInfo && (
          <div>
            <h4 className="font-medium mb-2">LIFF Information</h4>
            <Code className="block p-3 text-xs">
              {JSON.stringify(liffInfo, null, 2)}
            </Code>
          </div>
        )}

        <div>
          <h4 className="font-medium mb-2">Environment Check</h4>
          <div className="space-y-1 text-small">
            <p>LIFF ID: {process.env.NEXT_PUBLIC_LINE_LIFF_ID ? '✅ Set' : '❌ Not set'}</p>
            <p>User Agent: {typeof window !== 'undefined' ? window.navigator.userAgent.includes('Line') ? '✅ LINE App' : '❌ Not LINE App' : 'Unknown'}</p>
            <p>Protocol: {typeof window !== 'undefined' ? window.location.protocol === 'https:' ? '✅ HTTPS' : '⚠️ HTTP (OK for development)' : 'Unknown'}</p>
          </div>
        </div>

        <div className="flex gap-2 flex-wrap">
          {!liffInfo?.isLoggedIn && isReady && (
            <Button color="primary" onClick={handleLogin} size="sm">
              Login with LINE
            </Button>
          )}

          {liffInfo?.isLoggedIn && (
            <Button color="secondary" onClick={handleGetProfile} size="sm">
              Get Profile
            </Button>
          )}

          <Button
            color="default"
            onClick={() => window.open('https://liff-inspector.line.me/', '_blank')}
            size="sm"
          >
            Open LIFF Inspector
          </Button>
        </div>

        <div className="bg-blue-50 p-3 rounded-lg">
          <h4 className="font-medium text-blue-800 mb-2">Testing Instructions</h4>
          <div className="text-small text-blue-600 space-y-1">
            <p>1. <strong>For Development:</strong> Use LIFF Inspector at liff-inspector.line.me</p>
            <p>2. <strong>For Production:</strong> Access via LINE app with LIFF URL</p>
            <p>3. <strong>LIFF URL:</strong> https://liff.line.me/{process.env.NEXT_PUBLIC_LINE_LIFF_ID || 'YOUR_LIFF_ID'}</p>
          </div>
        </div>
      </CardBody>
    </Card>
  )
}
