'use client'

import { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { liffService, LiffProfile } from '@/lib/liff'
import { useAuth } from './AuthContext'
import type { User } from '@/lib/api'

interface LineAuthContextType {
  isLiffReady: boolean
  isLoggedIn: boolean
  profile: LiffProfile | null
  user: User | null
  loading: boolean
  login: () => Promise<void>
  logout: () => Promise<void>
  refreshProfile: () => Promise<void>
}

const LineAuthContext = createContext<LineAuthContextType | undefined>(undefined)

export function LineAuthProvider({ children }: { children: React.ReactNode }) {
  const [isLiffReady, setIsLiffReady] = useState(false)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [profile, setProfile] = useState<LiffProfile | null>(null)
  const [loading, setLoading] = useState(true)

  const { user, loginWithLine } = useAuth()

  const initializeLiff = useCallback(async () => {
    try {
      const success = await liffService.init()
      setIsLiffReady(success)

      if (success) {
        const loggedIn = liffService.isLoggedIn()
        setIsLoggedIn(loggedIn)

        if (loggedIn) {
          await refreshProfile()
        }
      }
    } catch (error) {
      console.error('LIFF initialization error:', error)
    } finally {
      setLoading(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const refreshProfile = async () => {
    try {
      const liffProfile = await liffService.getProfile()
      setProfile(liffProfile)

      if (liffProfile) {
        // Get LINE access token and login to our backend
        const accessToken = await liffService.getAccessToken()
        if (accessToken) {
          // Try to get email from ID token if available
          const idToken = await liffService.getDecodedIDToken()
          const email = idToken?.email || null

          await loginWithLine(accessToken, email)
        }
      }
    } catch (error) {
      console.error('Error refreshing profile:', error)
    }
  }

  const login = async () => {
    if (!isLiffReady) {
      throw new Error('LIFF not ready')
    }

    try {
      await liffService.login()
      // The login redirect will reload the page, so we don't need to update state here
    } catch (error) {
      console.error('Login error:', error)
      throw error
    }
  }

  const logout = async () => {
    if (!isLiffReady) {
      throw new Error('LIFF not ready')
    }

    try {
      await liffService.logout()
      setIsLoggedIn(false)
      setProfile(null)
    } catch (error) {
      console.error('Logout error:', error)
      throw error
    }
  }

  useEffect(() => {
    initializeLiff()
  }, [initializeLiff])

  return (
    <LineAuthContext.Provider value={{
      isLiffReady,
      isLoggedIn,
      profile,
      user,
      loading,
      login,
      logout,
      refreshProfile
    }}>
      {children}
    </LineAuthContext.Provider>
  )
}

export function useLineAuth() {
  const context = useContext(LineAuthContext)
  if (context === undefined) {
    throw new Error('useLineAuth must be used within a LineAuthProvider')
  }
  return context
}
