'use client'

import { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { apiClient, Admin } from '@/lib/api'

interface AdminAuthContextType {
  admin: Admin | null
  loading: boolean
  login: (email: string, password: string, branchId?: string) => Promise<boolean>
  logout: () => Promise<void>
  refreshAdmin: () => Promise<void>
}

const AdminAuthContext = createContext<AdminAuthContextType | undefined>(undefined)

export function AdminAuthProvider({ children }: { children: ReactNode }) {
  const [admin, setAdmin] = useState<Admin | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  const login = async (email: string, password: string, branchId?: string): Promise<boolean> => {
    try {
      const response = await apiClient.adminLogin(email, password, branchId)
      if (response.success) {
        apiClient.setToken(response.data.token)
        setAdmin(response.data.admin)

        // Role-based redirect
        if (response.data.admin.role === 'super_admin') {
          router.push('/admin')
        } else if (response.data.admin.role === 'branch_admin') {
          router.push('/admin/branch')
        }

        return true
      }
      return false
    } catch (error) {
      console.error('Admin login error:', error)
      return false
    }
  }

  const logout = async (): Promise<void> => {
    try {
      await apiClient.adminLogout()
    } catch (error) {
      console.error('Admin logout error:', error)
    } finally {
      apiClient.setToken(null)
      setAdmin(null)
      router.push('/admin/login')
    }
  }

  const refreshAdmin = useCallback(async (): Promise<void> => {
    try {
      const response = await apiClient.getAdminProfile()
      if (response.success) {
        setAdmin(response.data)
      } else {
        throw new Error('Failed to get admin profile')
      }
    } catch (error) {
      console.error('Failed to refresh admin:', error)
      apiClient.setToken(null)
      setAdmin(null)
      // Don't auto-redirect here, let the layout handle it
    }
  }, [])

  useEffect(() => {
    const initializeAdmin = async () => {
      const token = apiClient.getToken()
      if (token) {
        try {
          await refreshAdmin()
        } catch (error) {
          console.error('Failed to initialize admin:', error)
        }
      }
      setLoading(false)
    }

    initializeAdmin()
  }, [refreshAdmin])

  const value: AdminAuthContextType = {
    admin,
    loading,
    login,
    logout,
    refreshAdmin,
  }

  return (
    <AdminAuthContext.Provider value={value}>
      {children}
    </AdminAuthContext.Provider>
  )
}

export function useAdminAuth(): AdminAuthContextType {
  const context = useContext(AdminAuthContext)
  if (context === undefined) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider')
  }
  return context
}
