'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { apiClient, User } from '@/lib/api'

interface AuthContextType {
  user: User | null
  token: string | null
  loading: boolean
  signOut: () => Promise<void>
  refreshUser: () => Promise<void>
  loginWithLine: (accessToken: string, email?: string | null) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)

  const refreshUser = async () => {
    try {
      const storedToken = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null

      if (storedToken) {
        setToken(storedToken)
        apiClient.setToken(storedToken)

        try {
          const response = await apiClient.getProfile()
          if (response.success) {
            setUser(response.data)
          } else {
            // Token might be invalid, clear it
            setToken(null)
            apiClient.setToken(null)
            setUser(null)
          }
        } catch (error) {
          console.error('Error fetching user profile:', error)
          // Token might be invalid, clear it
          setToken(null)
          apiClient.setToken(null)
          setUser(null)
        }
      } else {
        setUser(null)
        setToken(null)
      }
    } catch (error) {
      console.error('Error refreshing user:', error)
      setUser(null)
      setToken(null)
    } finally {
      setLoading(false)
    }
  }

  const loginWithLine = async (accessToken: string, email?: string | null) => {
    try {
      const response = await apiClient.loginWithLine(accessToken, email)
      if (response.success) {
        setUser(response.data.user)
        setToken(response.data.token)
        apiClient.setToken(response.data.token)
      } else {
        throw new Error('Login failed')
      }
    } catch (error) {
      console.error('Login error:', error)
      throw error
    }
  }

  const signOut = async () => {
    try {
      if (token) {
        await apiClient.logout()
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setUser(null)
      setToken(null)
      apiClient.setToken(null)
    }
  }

  useEffect(() => {
    // Get initial session
    refreshUser()
  }, [])

  return (
    <AuthContext.Provider value={{
      user,
      token,
      loading,
      signOut,
      refreshUser,
      loginWithLine
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
