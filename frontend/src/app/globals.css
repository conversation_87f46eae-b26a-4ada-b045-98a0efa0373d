@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ensure proper font rendering for Thai text */
@layer base {
  html {
    font-family: var(--font-noto-sans-thai), system-ui, sans-serif;
  }

  body {
    font-feature-settings: "kern" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: rgb(var(--foreground));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background))
    )
    rgb(var(--background));
}

/* Custom scrollbar styles for AdminSidebar */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 10px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 10px;
    transition: background-color 0.3s ease;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #5FCED3;
  }

  .scrollbar-track-transparent::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background: #d1d5db;
  }

  .hover\:scrollbar-thumb-\[#5FCED3\]:hover::-webkit-scrollbar-thumb {
    background: #5FCED3;
  }
}
