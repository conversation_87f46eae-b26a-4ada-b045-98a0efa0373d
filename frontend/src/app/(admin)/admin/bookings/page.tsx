'use client'

import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import {
  <PERSON>ton,
  Card,
  CardBody,
  CardHeader,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Input,
  Select,
  SelectItem
} from "@heroui/react";
import { useRouter } from 'next/navigation';

interface Booking {
  id: string;
  customer_name: string;
  customer_email: string;
  class_name: string;
  date: string;
  time: string;
  status: 'confirmed' | 'pending' | 'cancelled';
  booking_date: string;
  price: number;
}

export default function BookingManagement() {
  const router = useRouter();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Mock data for demonstration
  useEffect(() => {
    const mockBookings: Booking[] = [
      {
        id: '1',
        customer_name: '<PERSON>',
        customer_email: '<EMAIL>',
        class_name: 'Be<PERSON>ner Pilates',
        date: '2024-01-15',
        time: '09:00',
        status: 'confirmed',
        booking_date: '2024-01-10',
        price: 500
      },
      {
        id: '2',
        customer_name: 'Mike Johnson',
        customer_email: '<EMAIL>',
        class_name: 'Intermediate Pilates',
        date: '2024-01-15',
        time: '10:30',
        status: 'pending',
        booking_date: '2024-01-12',
        price: 600
      },
      {
        id: '3',
        customer_name: 'Sarah Wilson',
        customer_email: '<EMAIL>',
        class_name: 'Mat Pilates',
        date: '2024-01-16',
        time: '18:00',
        status: 'confirmed',
        booking_date: '2024-01-11',
        price: 450
      },
      {
        id: '4',
        customer_name: 'David Brown',
        customer_email: '<EMAIL>',
        class_name: 'Advanced Pilates',
        date: '2024-01-17',
        time: '19:00',
        status: 'cancelled',
        booking_date: '2024-01-13',
        price: 700
      }
    ];

    setTimeout(() => {
      setBookings(mockBookings);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'success';
      case 'pending': return 'warning';
      case 'cancelled': return 'danger';
      default: return 'default';
    }
  };

  const handleStatusChange = (bookingId: string, newStatus: string) => {
    setBookings(prev => prev.map(booking =>
      booking.id === bookingId
        ? { ...booking, status: newStatus as 'confirmed' | 'pending' | 'cancelled' }
        : booking
    ));
  };

  const filteredBookings = bookings.filter(booking => {
    const matchesSearch = booking.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.customer_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.class_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const totalRevenue = bookings
    .filter(b => b.status === 'confirmed')
    .reduce((sum, b) => sum + b.price, 0);

  return (
    <AdminLayout
      title="Booking Management"
      subtitle="View and manage customer bookings"
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center gap-4">
                <Button
                  variant="light"
                  onPress={() => router.push('/admin')}
                  className="text-default-600"
                >
                  ← Back to Dashboard
                </Button>
                <div>
                  <h1 className="text-2xl font-bold text-primary">Booking Management</h1>
                  <p className="text-sm text-default-500">View and manage customer bookings</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardBody className="text-center">
                <div className="text-2xl font-bold text-primary">{bookings.length}</div>
                <p className="text-small text-default-600">Total Bookings</p>
              </CardBody>
            </Card>

            <Card>
              <CardBody className="text-center">
                <div className="text-2xl font-bold text-success">
                  {bookings.filter(b => b.status === 'confirmed').length}
                </div>
                <p className="text-small text-default-600">Confirmed</p>
              </CardBody>
            </Card>

            <Card>
              <CardBody className="text-center">
                <div className="text-2xl font-bold text-warning">
                  {bookings.filter(b => b.status === 'pending').length}
                </div>
                <p className="text-small text-default-600">Pending</p>
              </CardBody>
            </Card>

            <Card>
              <CardBody className="text-center">
                <div className="text-2xl font-bold text-secondary">฿{totalRevenue.toLocaleString()}</div>
                <p className="text-small text-default-600">Revenue</p>
              </CardBody>
            </Card>
          </div>

          {/* Filters */}
          <Card className="mb-6">
            <CardBody>
              <div className="flex gap-4 items-end">
                <Input
                  label="Search bookings"
                  placeholder="Search by customer name, email, or class..."
                  value={searchTerm}
                  onValueChange={setSearchTerm}
                  className="flex-1"
                />

                <Select
                  label="Status Filter"
                  selectedKeys={[statusFilter]}
                  onSelectionChange={(keys) => setStatusFilter(Array.from(keys)[0] as string)}
                  className="w-48"
                >
                  <SelectItem key="all">All Status</SelectItem>
                  <SelectItem key="confirmed">Confirmed</SelectItem>
                  <SelectItem key="pending">Pending</SelectItem>
                  <SelectItem key="cancelled">Cancelled</SelectItem>
                </Select>
              </div>
            </CardBody>
          </Card>

          {/* Bookings Table */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center w-full">
                <h2 className="text-lg font-semibold">All Bookings</h2>
                <Chip color="primary" variant="flat">
                  {filteredBookings.length} Results
                </Chip>
              </div>
            </CardHeader>
            <CardBody>
              <Table aria-label="Bookings table">
                <TableHeader>
                  <TableColumn>CUSTOMER</TableColumn>
                  <TableColumn>CLASS</TableColumn>
                  <TableColumn>DATE & TIME</TableColumn>
                  <TableColumn>BOOKING DATE</TableColumn>
                  <TableColumn>PRICE</TableColumn>
                  <TableColumn>STATUS</TableColumn>
                  <TableColumn>ACTIONS</TableColumn>
                </TableHeader>
                <TableBody isLoading={loading} emptyContent="No bookings found">
                  {filteredBookings.map((booking) => (
                    <TableRow key={booking.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{booking.customer_name}</p>
                          <p className="text-small text-default-500">{booking.customer_email}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <p className="font-medium">{booking.class_name}</p>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{booking.date}</p>
                          <p className="text-small text-default-500">{booking.time}</p>
                        </div>
                      </TableCell>
                      <TableCell>{booking.booking_date}</TableCell>
                      <TableCell>฿{booking.price}</TableCell>
                      <TableCell>
                        <Chip
                          color={getStatusColor(booking.status)}
                          size="sm"
                          variant="flat"
                        >
                          {booking.status}
                        </Chip>
                      </TableCell>
                      <TableCell>
                        <Dropdown>
                          <DropdownTrigger>
                            <Button variant="light" size="sm">
                              Actions
                            </Button>
                          </DropdownTrigger>
                          <DropdownMenu>
                            <DropdownItem key="confirm" onPress={() => handleStatusChange(booking.id, 'confirmed')}>
                              Confirm Booking
                            </DropdownItem>
                            <DropdownItem key="pending" onPress={() => handleStatusChange(booking.id, 'pending')}>
                              Mark as Pending
                            </DropdownItem>
                            <DropdownItem
                              key="cancel"
                              color="danger"
                              onPress={() => handleStatusChange(booking.id, 'cancelled')}
                            >
                              Cancel Booking
                            </DropdownItem>
                            <DropdownItem key="reminder">
                              Send Reminder
                            </DropdownItem>
                            <DropdownItem key="details">
                              View Details
                            </DropdownItem>
                          </DropdownMenu>
                        </Dropdown>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardBody>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
}
