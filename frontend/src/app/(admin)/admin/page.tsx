"use client";

import React, { useState, useEffect } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CardHeader,
  <PERSON>,
  <PERSON><PERSON>,
  Spinner,
} from "@heroui/react";
import {
  Users,
  Building2,
  Calendar,
  DollarSign,
  Activity,
  ArrowUpRight,
  Eye,
  Shield,
  BarChart3,
  TrendingUp,
  TrendingDown,
} from "lucide-react";
import Link from "next/link";
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import { useRouter } from 'next/navigation';

interface DashboardStats {
  totalBranches: number;
  activeBranches: number;
  totalAdmins: number;
  totalCustomers: number;
  totalRevenue: number;
  monthlyRevenue: number;
  revenueGrowth: number;
  activeBookings: number;
}

interface BranchOverview {
  id: number;
  name: string;
  address: string;
  totalCustomers: number;
  monthlyRevenue: number;
  activeBookings: number;
  status: 'active' | 'inactive';
  growth: number;
}

const mockStats: DashboardStats = {
  totalBranches: 5,
  activeBranches: 4,
  totalAdmins: 12,
  totalCustomers: 1250,
  totalRevenue: 2850000,
  monthlyRevenue: 485000,
  revenueGrowth: 12.5,
  activeBookings: 156,
};

const mockBranches: BranchOverview[] = [
  {
    id: 1,
    name: 'BodyLabs Sukhumvit',
    address: 'Sukhumvit Road, Bangkok',
    totalCustomers: 320,
    monthlyRevenue: 145000,
    activeBookings: 45,
    status: 'active',
    growth: 15.2
  },
  {
    id: 2,
    name: 'BodyLabs Silom',
    address: 'Silom Road, Bangkok',
    totalCustomers: 280,
    monthlyRevenue: 125000,
    activeBookings: 38,
    status: 'active',
    growth: 8.7
  },
  {
    id: 3,
    name: 'BodyLabs Thonglor',
    address: 'Thonglor District, Bangkok',
    totalCustomers: 195,
    monthlyRevenue: 95000,
    activeBookings: 28,
    status: 'active',
    growth: -2.1
  },
  {
    id: 4,
    name: 'BodyLabs Phuket',
    address: 'Patong Beach, Phuket',
    totalCustomers: 150,
    monthlyRevenue: 75000,
    activeBookings: 22,
    status: 'active',
    growth: 25.8
  }
];

const quickActions = [
  {
    title: "Branch Management",
    description: "Manage all branch locations",
    icon: <Building2 className="w-6 h-6" />,
    href: "/admin/branches",
    gradient: "from-[#5FCED3] to-[#5FCED3]/80",
  },
  {
    title: "Admin User Management",
    description: "Manage admin accounts and permissions",
    icon: <Shield className="w-6 h-6" />,
    href: "/admin/admin-users",
    gradient: "from-blue-500 to-blue-600",
  },
  {
    title: "System Analytics",
    description: "View system-wide insights and reports",
    icon: <BarChart3 className="w-6 h-6" />,
    href: "/admin/reports",
    gradient: "from-green-500 to-green-600",
  },
  {
    title: "Revenue Overview",
    description: "Monitor revenue across all branches",
    icon: <DollarSign className="w-6 h-6" />,
    href: "/admin/revenue",
    gradient: "from-purple-500 to-purple-600",
  },
];



export default function SuperAdminDashboard() {
  const { admin } = useAdminAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [stats] = useState<DashboardStats>(mockStats);
  const [branches] = useState<BranchOverview[]>(mockBranches);

  useEffect(() => {
    // Redirect if not super admin
    if (admin && admin.role !== 'super_admin') {
      router.push('/admin/branch');
      return;
    }

    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, [admin, router]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: 'THB',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'success';
    if (growth < 0) return 'danger';
    return 'default';
  };

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) return <TrendingUp className="w-4 h-4" />;
    if (growth < 0) return <TrendingDown className="w-4 h-4" />;
    return null;
  };

  if (loading) {
    return (
      <AdminLayout title="Super Admin Dashboard" subtitle="System overview and analytics">
        <div className="flex justify-center items-center h-64">
          <Spinner size="lg" color="primary" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Super Admin Dashboard"
      subtitle="System overview and analytics"
    >
      <div className="space-y-8">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-white to-[#5FCED3]/10 border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardBody className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 mb-1">
                    Total Branches
                  </p>
                  <p className="text-3xl font-bold text-slate-800">
                    {stats.totalBranches}
                  </p>
                  <div className="flex items-center mt-2">
                    <Chip
                      size="sm"
                      variant="flat"
                      color="success"
                      className="text-xs"
                    >
                      {stats.activeBranches} active
                    </Chip>
                  </div>
                </div>
                <div className="p-3 bg-[#5FCED3]/20 rounded-xl">
                  <Building2 className="w-8 h-8 text-[#5FCED3]" />
                </div>
              </div>
            </CardBody>
          </Card>

          <Card className="bg-gradient-to-br from-white to-blue-50 border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardBody className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 mb-1">
                    Total Customers
                  </p>
                  <p className="text-3xl font-bold text-slate-800">
                    {stats.totalCustomers.toLocaleString()}
                  </p>
                  <div className="flex items-center mt-2">
                    <Chip
                      size="sm"
                      variant="flat"
                      color="default"
                      className="text-xs"
                    >
                      Across all branches
                    </Chip>
                  </div>
                </div>
                <div className="p-3 bg-blue-100 rounded-xl">
                  <Users className="w-8 h-8 text-blue-600" />
                </div>
              </div>
            </CardBody>
          </Card>

          <Card className="bg-gradient-to-br from-white to-green-50 border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardBody className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 mb-1">
                    Monthly Revenue
                  </p>
                  <p className="text-3xl font-bold text-slate-800">
                    {formatCurrency(stats.monthlyRevenue)}
                  </p>
                  <div className="flex items-center mt-2">
                    {getGrowthIcon(stats.revenueGrowth)}
                    <Chip
                      size="sm"
                      variant="flat"
                      color={getGrowthColor(stats.revenueGrowth)}
                      className="text-xs ml-1"
                    >
                      {stats.revenueGrowth}% vs last month
                    </Chip>
                  </div>
                </div>
                <div className="p-3 bg-green-100 rounded-xl">
                  <DollarSign className="w-8 h-8 text-green-600" />
                </div>
              </div>
            </CardBody>
          </Card>

          <Card className="bg-gradient-to-br from-white to-orange-50 border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardBody className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 mb-1">
                    Active Bookings
                  </p>
                  <p className="text-3xl font-bold text-slate-800">
                    {stats.activeBookings}
                  </p>
                  <div className="flex items-center mt-2">
                    <Activity className="w-4 h-4 text-orange-500 mr-1" />
                    <span className="text-xs text-slate-600">
                      Current sessions
                    </span>
                  </div>
                </div>
                <div className="p-3 bg-orange-100 rounded-xl">
                  <Calendar className="w-8 h-8 text-orange-600" />
                </div>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {quickActions.map((action, index) => (
            <Link key={index} href={action.href}>
              <Card className="group bg-white border-0 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 cursor-pointer overflow-hidden">
                <div className={`h-2 bg-gradient-to-r ${action.gradient}`}></div>
                <CardBody className="p-6">
                  <div className="flex items-center space-x-4 mb-4">
                    <div className={`p-3 rounded-xl bg-gradient-to-r ${action.gradient} text-white group-hover:scale-110 transition-transform duration-300`}>
                      {action.icon}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-slate-800 group-hover:text-slate-900">
                        {action.title}
                      </h3>
                      <p className="text-sm text-slate-600">
                        {action.description}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center text-slate-500 group-hover:text-slate-700 transition-colors">
                    <span className="text-sm font-medium">Manage</span>
                    <ArrowUpRight className="w-4 h-4 ml-1 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                  </div>
                </CardBody>
              </Card>
            </Link>
          ))}
        </div>

        {/* Branch Overview */}
        <Card className="bg-white border-0 shadow-lg">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-semibold text-slate-800">
                  Branch Overview
                </h3>
                <p className="text-sm text-slate-600">Performance across all locations</p>
              </div>
              <Button
                variant="bordered"
                size="sm"
                startContent={<Eye className="w-4 h-4" />}
                onPress={() => router.push('/admin/branches')}
              >
                View All
              </Button>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-4">
              {branches.map((branch) => (
                <div
                  key={branch.id}
                  className="flex items-center justify-between p-4 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors"
                >
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-[#5FCED3]/10 rounded-lg">
                      <Building2 className="w-5 h-5 text-[#5FCED3]" />
                    </div>
                    <div>
                      <h4 className="font-medium text-slate-800">{branch.name}</h4>
                      <p className="text-sm text-slate-500">{branch.address}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-6">
                    <div className="text-center">
                      <p className="text-sm font-medium text-slate-800">{branch.totalCustomers}</p>
                      <p className="text-xs text-slate-500">Customers</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm font-medium text-slate-800">{formatCurrency(branch.monthlyRevenue)}</p>
                      <p className="text-xs text-slate-500">Monthly Revenue</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm font-medium text-slate-800">{branch.activeBookings}</p>
                      <p className="text-xs text-slate-500">Active Bookings</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Chip
                        size="sm"
                        color={branch.status === 'active' ? 'success' : 'default'}
                        variant="flat"
                      >
                        {branch.status}
                      </Chip>
                      {branch.growth !== 0 && (
                        <Chip
                          size="sm"
                          color={getGrowthColor(branch.growth)}
                          variant="flat"
                          startContent={getGrowthIcon(branch.growth)}
                        >
                          {Math.abs(branch.growth)}%
                        </Chip>
                      )}
                    </div>
                    <Button
                      size="sm"
                      variant="light"
                      onPress={() => router.push(`/admin/branch/${branch.id}`)}
                    >
                      Manage
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      </div>
    </AdminLayout>
  );
}