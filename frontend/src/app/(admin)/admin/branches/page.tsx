'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Input,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Chip,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Pagination,

  Textarea,
  Card,
  CardBody,
  Spinner,
} from '@heroui/react';
import {
  Plus,
  Search,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  ToggleLeft,
  ToggleRight,
  MapPin,
  Phone,
  Mail,
  Clock,
  Calendar,
  Users,
  BookOpen,
  Activity
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { apiClient } from '@/lib/api';
import type {
  Branch,
  CreateBranchRequest,
  BranchQueryParams,
  BranchStats
} from '@/lib/api';
import AdminLayout from '@/components/admin/AdminLayout';
import AdminLoading from '@/components/admin/AdminLoading';

const OPERATING_DAYS = [
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday',
  'sunday'
];

const DAYS_DISPLAY = {
  monday: 'Monday',
  tuesday: 'Tuesday',
  wednesday: 'Wednesday',
  thursday: 'Thursday',
  friday: 'Friday',
  saturday: 'Saturday',
  sunday: 'Sunday'
};

export default function BranchManagementPage() {
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loading, setLoading] = useState(true);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [search, setSearch] = useState('');
  const [selectedBranch, setSelectedBranch] = useState<Branch | null>(null);
  const [branchStats, setBranchStats] = useState<BranchStats | null>(null);
  const [formData, setFormData] = useState<CreateBranchRequest>({
    name: '',
    address: '',
    phoneNumber: '',
    email: '',
    openTime: '06:00',
    closeTime: '22:00',
    operatingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    isActive: true,
  });
  const [isEditing, setIsEditing] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const {
    isOpen: isCreateModalOpen,
    onOpen: onCreateModalOpen,
    onClose: onCreateModalClose
  } = useDisclosure();

  const {
    isOpen: isViewModalOpen,
    onOpen: onViewModalOpen,
    onClose: onViewModalClose
  } = useDisclosure();

  const fetchBranches = useCallback(async () => {
    try {
      setLoading(true);
      const params: BranchQueryParams = {
        page,
        limit,
        ...(search && { search }),
      };

      const response = await apiClient.getBranches(params);
      if (response.success) {
        setBranches(response.data.branches);
        setTotal(response.data.total);
      }
    } catch (error) {
      console.error('Error fetching branches:', error);
      toast.error('Failed to fetch branches');
    } finally {
      setLoading(false);
    }
  }, [page, limit, search]);

  const fetchBranchStats = async (branchId: number) => {
    try {
      const response = await apiClient.getBranchStats({ branchId });
      if (response.success) {
        setBranchStats(response.data);
      }
    } catch (error) {
      console.error('Error fetching branch stats:', error);
    }
  };

  useEffect(() => {
    fetchBranches();
  }, [fetchBranches]);

  const handleCreateBranch = async () => {
    try {
      setSubmitting(true);
      const response = await apiClient.createBranch(formData);
      if (response.success) {
        toast.success('Branch created successfully');
        fetchBranches();
        onCreateModalClose();
        resetForm();
      }
    } catch (error) {
      console.error('Error creating branch:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create branch';
      toast.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdateBranch = async () => {
    if (!selectedBranch) return;

    try {
      setSubmitting(true);
      const response = await apiClient.updateBranch(selectedBranch.id, formData);
      if (response.success) {
        toast.success('Branch updated successfully');
        fetchBranches();
        onCreateModalClose();
        resetForm();
      }
    } catch (error) {
      console.error('Error updating branch:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update branch';
      toast.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteBranch = async (branch: Branch) => {
    if (!confirm(`Are you sure you want to delete "${branch.name}"?`)) return;

    try {
      const response = await apiClient.deleteBranch(branch.id);
      if (response.success) {
        toast.success('Branch deleted successfully');
        fetchBranches();
      }
    } catch (error) {
      console.error('Error deleting branch:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete branch';
      toast.error(errorMessage);
    }
  };

  const handleToggleStatus = async (branch: Branch) => {
    try {
      const response = await apiClient.toggleBranchStatus(branch.id);
      if (response.success) {
        toast.success(`Branch ${response.data.isActive ? 'activated' : 'deactivated'} successfully`);
        fetchBranches();
      }
    } catch (error) {
      console.error('Error toggling branch status:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to toggle branch status';
      toast.error(errorMessage);
    }
  };

  const openCreateModal = () => {
    setIsEditing(false);
    setSelectedBranch(null);
    resetForm();
    onCreateModalOpen();
  };

  const openEditModal = (branch: Branch) => {
    setIsEditing(true);
    setSelectedBranch(branch);
    setFormData({
      name: branch.name,
      address: branch.address,
      phoneNumber: branch.phoneNumber || '',
      email: branch.email || '',
      openTime: branch.openTime || '06:00',
      closeTime: branch.closeTime || '22:00',
      operatingDays: branch.operatingDays || [],
      isActive: branch.isActive,
    });
    onCreateModalOpen();
  };

  const openViewModal = async (branch: Branch) => {
    setSelectedBranch(branch);
    await fetchBranchStats(branch.id);
    onViewModalOpen();
  };

  const resetForm = () => {
    setFormData({
      name: '',
      address: '',
      phoneNumber: '',
      email: '',
      openTime: '06:00',
      closeTime: '22:00',
      operatingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
      isActive: true,
    });
  };

  const handleOperatingDaysChange = (day: string) => {
    setFormData(prev => ({
      ...prev,
      operatingDays: prev.operatingDays.includes(day)
        ? prev.operatingDays.filter(d => d !== day)
        : [...prev.operatingDays, day]
    }));
  };

  if (loading && branches.length === 0) {
    return (
      <AdminLayout title="Branch Management" subtitle="Manage your business locations">
        <AdminLoading message="Loading branches..." />
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Branch Management"
      subtitle="Manage your business locations"
    >
      <div className="space-y-6">
        {/* Action Bar */}
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Branches</h2>
            <p className="text-gray-600 mt-1">Manage your business locations</p>
          </div>
          <Button
            color="primary"
            startContent={<Plus size={20} />}
            onPress={openCreateModal}
            className="bg-gradient-to-r from-[#5FCED3] to-[#4FB3D9] text-white font-medium"
          >
            Add Branch
          </Button>
        </div>
        {/* Search and Filters */}
        <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
          <CardBody className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <Input
                placeholder="Search branches..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                startContent={<Search size={20} className="text-gray-400" />}
                className="flex-1"
                classNames={{
                  input: "text-sm",
                  inputWrapper: "border-gray-200 hover:border-[#5FCED3] focus-within:border-[#5FCED3]"
                }}
              />
            </div>
          </CardBody>
        </Card>

        {/* Branches Table */}
        <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
          <CardBody className="p-0">
            <Table
              aria-label="Branches table"
              classNames={{
                wrapper: "shadow-none border-0",
                th: "bg-gray-50/80 text-gray-700 font-semibold",
                td: "border-b border-gray-100"
              }}
            >
              <TableHeader>
                <TableColumn>BRANCH</TableColumn>
                <TableColumn>CONTACT</TableColumn>
                <TableColumn>OPERATING HOURS</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>ACTIONS</TableColumn>
              </TableHeader>
              <TableBody
                emptyContent={
                  <div className="text-center py-8">
                    <MapPin size={48} className="mx-auto text-gray-300 mb-4" />
                    <p className="text-gray-500">No branches found</p>
                  </div>
                }
                isLoading={loading}
                loadingContent={<Spinner color="primary" />}
              >
                {branches.map((branch) => (
                  <TableRow key={branch.id}>
                    <TableCell>
                      <div>
                        <p className="font-semibold text-gray-900">{branch.name}</p>
                        <p className="text-sm text-gray-500 flex items-center gap-1">
                          <MapPin size={14} />
                          {branch.address}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {branch.phoneNumber && (
                          <p className="text-sm text-gray-600 flex items-center gap-1">
                            <Phone size={14} />
                            {branch.phoneNumber}
                          </p>
                        )}
                        {branch.email && (
                          <p className="text-sm text-gray-600 flex items-center gap-1">
                            <Mail size={14} />
                            {branch.email}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="text-sm text-gray-600 flex items-center gap-1">
                          <Clock size={14} />
                          {branch.openTime} - {branch.closeTime}
                        </p>
                        <p className="text-xs text-gray-500 flex items-center gap-1">
                          <Calendar size={12} />
                          {branch.operatingDays?.length || 0} days/week
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Chip
                        color={branch.isActive ? "success" : "danger"}
                        variant="flat"
                        size="sm"
                      >
                        {branch.isActive ? "Active" : "Inactive"}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <Dropdown>
                        <DropdownTrigger>
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                          >
                            <MoreVertical size={16} />
                          </Button>
                        </DropdownTrigger>
                        <DropdownMenu>
                          <DropdownItem
                            key="view"
                            startContent={<Eye size={16} />}
                            onPress={() => openViewModal(branch)}
                          >
                            View Details
                          </DropdownItem>
                          <DropdownItem
                            key="edit"
                            startContent={<Edit size={16} />}
                            onPress={() => openEditModal(branch)}
                          >
                            Edit
                          </DropdownItem>
                          <DropdownItem
                            key="toggle"
                            startContent={branch.isActive ? <ToggleLeft size={16} /> : <ToggleRight size={16} />}
                            onPress={() => handleToggleStatus(branch)}
                          >
                            {branch.isActive ? 'Deactivate' : 'Activate'}
                          </DropdownItem>
                          <DropdownItem
                            key="delete"
                            className="text-danger"
                            color="danger"
                            startContent={<Trash2 size={16} />}
                            onPress={() => handleDeleteBranch(branch)}
                          >
                            Delete
                          </DropdownItem>
                        </DropdownMenu>
                      </Dropdown>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardBody>
        </Card>

        {/* Pagination */}
        {total > limit && (
          <div className="flex justify-center">
            <Pagination
              total={Math.ceil(total / limit)}
              page={page}
              onChange={setPage}
              color="primary"
              showControls
              classNames={{
                cursor: "bg-[#5FCED3] text-white"
              }}
            />
          </div>
        )}
      </div>

      {/* Create/Edit Branch Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={onCreateModalClose}
        size="2xl"
        scrollBehavior="inside"
        classNames={{
          base: "bg-white",
          header: "border-b border-gray-200",
          body: "py-6",
          footer: "border-t border-gray-200"
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h3 className="text-xl font-semibold text-gray-900">
              {isEditing ? 'Edit Branch' : 'Create New Branch'}
            </h3>
            <p className="text-sm text-gray-500">
              {isEditing ? 'Update branch information' : 'Add a new branch location'}
            </p>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Basic Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Branch Name"
                    placeholder="Enter branch name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    isRequired
                    classNames={{
                      input: "text-sm",
                      inputWrapper: "border-gray-200 hover:border-[#5FCED3] focus-within:border-[#5FCED3]"
                    }}
                  />
                  <Input
                    label="Phone Number"
                    placeholder="Enter phone number"
                    value={formData.phoneNumber}
                    onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                    classNames={{
                      input: "text-sm",
                      inputWrapper: "border-gray-200 hover:border-[#5FCED3] focus-within:border-[#5FCED3]"
                    }}
                  />
                </div>
                <Textarea
                  label="Address"
                  placeholder="Enter full address"
                  value={formData.address}
                  onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                  isRequired
                  classNames={{
                    input: "text-sm",
                    inputWrapper: "border-gray-200 hover:border-[#5FCED3] focus-within:border-[#5FCED3]"
                  }}
                />
                <Input
                  label="Email"
                  type="email"
                  placeholder="Enter email address"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  classNames={{
                    input: "text-sm",
                    inputWrapper: "border-gray-200 hover:border-[#5FCED3] focus-within:border-[#5FCED3]"
                  }}
                />
              </div>

              {/* Operating Hours */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Operating Hours</h4>
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    label="Opening Time"
                    type="time"
                    value={formData.openTime}
                    onChange={(e) => setFormData(prev => ({ ...prev, openTime: e.target.value }))}
                    isRequired
                    classNames={{
                      input: "text-sm",
                      inputWrapper: "border-gray-200 hover:border-[#5FCED3] focus-within:border-[#5FCED3]"
                    }}
                  />
                  <Input
                    label="Closing Time"
                    type="time"
                    value={formData.closeTime}
                    onChange={(e) => setFormData(prev => ({ ...prev, closeTime: e.target.value }))}
                    isRequired
                    classNames={{
                      input: "text-sm",
                      inputWrapper: "border-gray-200 hover:border-[#5FCED3] focus-within:border-[#5FCED3]"
                    }}
                  />
                </div>
              </div>

              {/* Operating Days */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Operating Days</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {OPERATING_DAYS.map((day) => (
                    <Button
                      key={day}
                      size="sm"
                      variant={formData.operatingDays.includes(day) ? "solid" : "bordered"}
                      color={formData.operatingDays.includes(day) ? "primary" : "default"}
                      onPress={() => handleOperatingDaysChange(day)}
                      className={formData.operatingDays.includes(day)
                        ? "bg-[#5FCED3] text-white"
                        : "border-gray-200 text-gray-600 hover:border-[#5FCED3]"
                      }
                    >
                      {DAYS_DISPLAY[day as keyof typeof DAYS_DISPLAY]}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              variant="light"
              onPress={onCreateModalClose}
              disabled={submitting}
            >
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={isEditing ? handleUpdateBranch : handleCreateBranch}
              isLoading={submitting}
              className="bg-gradient-to-r from-[#5FCED3] to-[#4FB3D9] text-white"
            >
              {isEditing ? 'Update Branch' : 'Create Branch'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* View Branch Details Modal */}
      <Modal
        isOpen={isViewModalOpen}
        onClose={onViewModalClose}
        size="3xl"
        scrollBehavior="inside"
        classNames={{
          base: "bg-white",
          header: "border-b border-gray-200",
          body: "py-6"
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h3 className="text-xl font-semibold text-gray-900">
              {selectedBranch?.name}
            </h3>
            <p className="text-sm text-gray-500">Branch Details & Statistics</p>
          </ModalHeader>
          <ModalBody>
            {selectedBranch && (
              <div className="space-y-6">
                {/* Branch Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className="border border-gray-200">
                    <CardBody className="p-4">
                      <h4 className="font-medium text-gray-900 mb-3">Contact Information</h4>
                      <div className="space-y-2">
                        <p className="text-sm text-gray-600 flex items-center gap-2">
                          <MapPin size={16} className="text-[#5FCED3]" />
                          {selectedBranch.address}
                        </p>
                        {selectedBranch.phoneNumber && (
                          <p className="text-sm text-gray-600 flex items-center gap-2">
                            <Phone size={16} className="text-[#5FCED3]" />
                            {selectedBranch.phoneNumber}
                          </p>
                        )}
                        {selectedBranch.email && (
                          <p className="text-sm text-gray-600 flex items-center gap-2">
                            <Mail size={16} className="text-[#5FCED3]" />
                            {selectedBranch.email}
                          </p>
                        )}
                      </div>
                    </CardBody>
                  </Card>

                  <Card className="border border-gray-200">
                    <CardBody className="p-4">
                      <h4 className="font-medium text-gray-900 mb-3">Operating Hours</h4>
                      <div className="space-y-2">
                        <p className="text-sm text-gray-600 flex items-center gap-2">
                          <Clock size={16} className="text-[#5FCED3]" />
                          {selectedBranch.openTime} - {selectedBranch.closeTime}
                        </p>
                        <p className="text-sm text-gray-600 flex items-center gap-2">
                          <Calendar size={16} className="text-[#5FCED3]" />
                          {selectedBranch.operatingDays?.map(day =>
                            DAYS_DISPLAY[day as keyof typeof DAYS_DISPLAY]
                          ).join(', ')}
                        </p>
                        <Chip
                          color={selectedBranch.isActive ? "success" : "danger"}
                          variant="flat"
                          size="sm"
                        >
                          {selectedBranch.isActive ? "Active" : "Inactive"}
                        </Chip>
                      </div>
                    </CardBody>
                  </Card>
                </div>

                {/* Statistics */}
                {branchStats && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Branch Statistics</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <Card className="border border-gray-200">
                        <CardBody className="p-4 text-center">
                          <Users size={24} className="mx-auto text-[#5FCED3] mb-2" />
                          <p className="text-2xl font-bold text-gray-900">{branchStats.activeUsers}</p>
                          <p className="text-sm text-gray-500">Active Users</p>
                        </CardBody>
                      </Card>
                      <Card className="border border-gray-200">
                        <CardBody className="p-4 text-center">
                          <BookOpen size={24} className="mx-auto text-[#5FCED3] mb-2" />
                          <p className="text-2xl font-bold text-gray-900">{branchStats.activeCourses}</p>
                          <p className="text-sm text-gray-500">Active Courses</p>
                        </CardBody>
                      </Card>
                      <Card className="border border-gray-200">
                        <CardBody className="p-4 text-center">
                          <Activity size={24} className="mx-auto text-[#5FCED3] mb-2" />
                          <p className="text-2xl font-bold text-gray-900">{branchStats.completedBookings}</p>
                          <p className="text-sm text-gray-500">Completed Sessions</p>
                        </CardBody>
                      </Card>
                      <Card className="border border-gray-200">
                        <CardBody className="p-4 text-center">
                          <div className="text-[#5FCED3] mb-2">฿</div>
                          <p className="text-2xl font-bold text-gray-900">{branchStats.totalRevenue.toLocaleString()}</p>
                          <p className="text-sm text-gray-500">Total Revenue</p>
                        </CardBody>
                      </Card>
                    </div>
                  </div>
                )}
              </div>
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </AdminLayout>
  );
}
