'use client'

import { useState } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import {
  <PERSON>ton,
  Card,
  CardBody,
  CardHeader,
  Input,
  Switch,
  Select,
  SelectItem
} from "@heroui/react";
export default function SystemSettings() {

  const [settings, setSettings] = useState({
    // Business Settings
    businessName: 'BodyLab Pilates',
    businessEmail: '<EMAIL>',
    businessPhone: '+66 2 123 4567',
    businessAddress: 'Bangkok, Thailand',

    // Booking Settings
    maxAdvanceBookingDays: 30,
    cancellationDeadlineHours: 24,
    autoConfirmBookings: true,
    allowWaitlist: true,

    // Notification Settings
    emailNotifications: true,
    smsNotifications: false,
    lineNotifications: true,
    reminderHours: 24,

    // Payment Settings
    currency: 'THB',
    taxRate: 7,
    acceptCash: true,
    acceptCard: true,
    acceptTransfer: true,

    // System Settings
    timezone: 'Asia/Bangkok',
    language: 'th',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h'
  });

  const handleSave = () => {
    // TODO: Implement save functionality
    console.log('Saving settings:', settings);
    alert('Settings saved successfully!');
  };

  const handleReset = () => {
    // Reset to default values
    setSettings({
      businessName: 'BodyLab Pilates',
      businessEmail: '<EMAIL>',
      businessPhone: '+66 2 123 4567',
      businessAddress: 'Bangkok, Thailand',
      maxAdvanceBookingDays: 30,
      cancellationDeadlineHours: 24,
      autoConfirmBookings: true,
      allowWaitlist: true,
      emailNotifications: true,
      smsNotifications: false,
      lineNotifications: true,
      reminderHours: 24,
      currency: 'THB',
      taxRate: 7,
      acceptCash: true,
      acceptCard: true,
      acceptTransfer: true,
      timezone: 'Asia/Bangkok',
      language: 'th',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h'
    });
  };

  return (
    <AdminLayout
      title="System Settings"
      subtitle="Configure your studio settings and preferences"
    >
      <div className="space-y-8">
        {/* Action Buttons */}
        <div className="flex justify-end gap-2 mb-6">
          <Button variant="light" onPress={handleReset}>
            Reset to Default
          </Button>
          <Button color="primary" onPress={handleSave}>
            Save Changes
          </Button>
        </div>
        {/* Business Information */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Business Information</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Business Name"
                value={settings.businessName}
                onValueChange={(value) => setSettings({ ...settings, businessName: value })}
              />
              <Input
                label="Business Email"
                type="email"
                value={settings.businessEmail}
                onValueChange={(value) => setSettings({ ...settings, businessEmail: value })}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Business Phone"
                value={settings.businessPhone}
                onValueChange={(value) => setSettings({ ...settings, businessPhone: value })}
              />
              <Input
                label="Business Address"
                value={settings.businessAddress}
                onValueChange={(value) => setSettings({ ...settings, businessAddress: value })}
              />
            </div>
          </CardBody>
        </Card>

        {/* Booking Settings */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Booking Settings</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Max Advance Booking (Days)"
                type="number"
                value={settings.maxAdvanceBookingDays.toString()}
                onValueChange={(value) => setSettings({ ...settings, maxAdvanceBookingDays: parseInt(value) || 0 })}
              />
              <Input
                label="Cancellation Deadline (Hours)"
                type="number"
                value={settings.cancellationDeadlineHours.toString()}
                onValueChange={(value) => setSettings({ ...settings, cancellationDeadlineHours: parseInt(value) || 0 })}
              />
            </div>

            <div className="flex flex-col gap-4">
              <Switch
                isSelected={settings.autoConfirmBookings}
                onValueChange={(value) => setSettings({ ...settings, autoConfirmBookings: value })}
              >
                Auto-confirm bookings
              </Switch>
              <Switch
                isSelected={settings.allowWaitlist}
                onValueChange={(value) => setSettings({ ...settings, allowWaitlist: value })}
              >
                Allow waitlist when class is full
              </Switch>
            </div>
          </CardBody>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Notification Settings</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="flex flex-col gap-4">
              <Switch
                isSelected={settings.emailNotifications}
                onValueChange={(value) => setSettings({ ...settings, emailNotifications: value })}
              >
                Email notifications
              </Switch>
              <Switch
                isSelected={settings.smsNotifications}
                onValueChange={(value) => setSettings({ ...settings, smsNotifications: value })}
              >
                SMS notifications
              </Switch>
              <Switch
                isSelected={settings.lineNotifications}
                onValueChange={(value) => setSettings({ ...settings, lineNotifications: value })}
              >
                LINE notifications
              </Switch>
            </div>

            <Input
              label="Reminder Hours Before Class"
              type="number"
              value={settings.reminderHours.toString()}
              onValueChange={(value) => setSettings({ ...settings, reminderHours: parseInt(value) || 0 })}
              className="max-w-xs"
            />
          </CardBody>
        </Card>

        {/* Payment Settings */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Payment Settings</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Currency"
                selectedKeys={[settings.currency]}
                onSelectionChange={(keys) => setSettings({ ...settings, currency: Array.from(keys)[0] as string })}
              >
                <SelectItem key="THB">Thai Baht (THB)</SelectItem>
                <SelectItem key="USD">US Dollar (USD)</SelectItem>
                <SelectItem key="EUR">Euro (EUR)</SelectItem>
              </Select>

              <Input
                label="Tax Rate (%)"
                type="number"
                value={settings.taxRate.toString()}
                onValueChange={(value) => setSettings({ ...settings, taxRate: parseInt(value) || 0 })}
              />
            </div>

            <div className="flex flex-col gap-4">
              <Switch
                isSelected={settings.acceptCash}
                onValueChange={(value) => setSettings({ ...settings, acceptCash: value })}
              >
                Accept cash payments
              </Switch>
              <Switch
                isSelected={settings.acceptCard}
                onValueChange={(value) => setSettings({ ...settings, acceptCard: value })}
              >
                Accept card payments
              </Switch>
              <Switch
                isSelected={settings.acceptTransfer}
                onValueChange={(value) => setSettings({ ...settings, acceptTransfer: value })}
              >
                Accept bank transfers
              </Switch>
            </div>
          </CardBody>
        </Card>

        {/* System Settings */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">System Settings</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Timezone"
                selectedKeys={[settings.timezone]}
                onSelectionChange={(keys) => setSettings({ ...settings, timezone: Array.from(keys)[0] as string })}
              >
                <SelectItem key="Asia/Bangkok">Asia/Bangkok</SelectItem>
                <SelectItem key="UTC">UTC</SelectItem>
                <SelectItem key="America/New_York">America/New_York</SelectItem>
              </Select>

              <Select
                label="Default Language"
                selectedKeys={[settings.language]}
                onSelectionChange={(keys) => setSettings({ ...settings, language: Array.from(keys)[0] as string })}
              >
                <SelectItem key="th">Thai</SelectItem>
                <SelectItem key="en">English</SelectItem>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Date Format"
                selectedKeys={[settings.dateFormat]}
                onSelectionChange={(keys) => setSettings({ ...settings, dateFormat: Array.from(keys)[0] as string })}
              >
                <SelectItem key="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                <SelectItem key="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                <SelectItem key="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
              </Select>

              <Select
                label="Time Format"
                selectedKeys={[settings.timeFormat]}
                onSelectionChange={(keys) => setSettings({ ...settings, timeFormat: Array.from(keys)[0] as string })}
              >
                <SelectItem key="24h">24 Hour</SelectItem>
                <SelectItem key="12h">12 Hour (AM/PM)</SelectItem>
              </Select>
            </div>
          </CardBody>
        </Card>
      </div>
    </AdminLayout>
  );
}
