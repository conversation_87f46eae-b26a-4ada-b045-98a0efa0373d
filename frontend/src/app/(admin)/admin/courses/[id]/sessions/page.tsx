'use client'

import { useState, useEffect, useCallback } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Spinner,
  Breadcrumbs,
  BreadcrumbItem,
} from "@heroui/react"
import { ArrowLeftIcon } from '@heroicons/react/24/outline'
import { apiClient, Course, CourseSession } from '@/lib/api'
import { toast } from 'react-hot-toast'

export default function CourseSessionsPage() {
  const params = useParams()
  const router = useRouter()
  const courseId = parseInt(params.id as string)

  const [course, setCourse] = useState<Course | null>(null)
  const [sessions, setSessions] = useState<CourseSession[]>([])
  const [loading, setLoading] = useState(true)

  const loadCourseData = useCallback(async () => {
    try {
      const response = await apiClient.getAdminCourseById(courseId)
      if (response.success) {
        setCourse(response.data)
      }
    } catch (error) {
      console.error('Error loading course:', error)
      toast.error('Failed to load course details')
    }
  }, [courseId])

  const loadSessions = useCallback(async () => {
    try {
      setLoading(true)
      const response = await apiClient.getCourseSessions(courseId)
      if (response.success) {
        setSessions(response.data)
      }
    } catch (error) {
      console.error('Error loading sessions:', error)
      toast.error('Failed to load course sessions')
    } finally {
      setLoading(false)
    }
  }, [courseId])

  useEffect(() => {
    if (courseId) {
      loadCourseData()
      loadSessions()
    }
  }, [courseId, loadCourseData, loadSessions])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'success'
      case 'booked': return 'primary'
      case 'completed': return 'secondary'
      case 'cancelled': return 'danger'
      default: return 'default'
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not scheduled'
    return new Date(dateString).toLocaleDateString('th-TH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading && !course) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    )
  }

  if (!course) {
    return (
      <div className="text-center py-8">
        <p className="text-lg text-default-500">Course not found</p>
        <Button
          color="primary"
          onPress={() => router.push('/admin/courses')}
          className="mt-4"
        >
          Back to Courses
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumbs>
        <BreadcrumbItem onPress={() => router.push('/admin/courses')}>
          Courses
        </BreadcrumbItem>
        <BreadcrumbItem>{course.name}</BreadcrumbItem>
        <BreadcrumbItem>Sessions</BreadcrumbItem>
      </Breadcrumbs>

      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          isIconOnly
          variant="light"
          onPress={() => router.push('/admin/courses')}
        >
          <ArrowLeftIcon className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">{course.name} - Sessions</h1>
          <p className="text-default-600">
            Customer: {course.customer.displayName} |
            Sessions: {course.usedSessions}/{course.totalSessions} |
            Remaining: {course.remainingSessions}
          </p>
        </div>
      </div>

      {/* Course Summary */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Course Summary</h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm text-default-500">Total Sessions</p>
              <p className="text-2xl font-bold">{course.totalSessions}</p>
            </div>
            <div>
              <p className="text-sm text-default-500">Used Sessions</p>
              <p className="text-2xl font-bold text-primary">{course.usedSessions}</p>
            </div>
            <div>
              <p className="text-sm text-default-500">Remaining Sessions</p>
              <p className="text-2xl font-bold text-success">{course.remainingSessions}</p>
            </div>
            <div>
              <p className="text-sm text-default-500">Status</p>
              <Chip
                color={course.status === 'active' ? 'success' : course.status === 'expired' ? 'danger' : 'warning'}
                variant="flat"
                size="sm"
                className="mt-1"
              >
                {course.status}
              </Chip>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Sessions Table */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Session Details</h3>
        </CardHeader>
        <CardBody className="p-0">
          {loading ? (
            <div className="flex justify-center items-center h-32">
              <Spinner />
            </div>
          ) : (
            <Table aria-label="Course sessions table">
              <TableHeader>
                <TableColumn>SESSION #</TableColumn>
                <TableColumn>SCHEDULED DATE</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>BOOKING ID</TableColumn>
                <TableColumn>NOTES</TableColumn>
                <TableColumn>CREATED</TableColumn>
              </TableHeader>
              <TableBody emptyContent="No sessions found">
                {sessions.map((session, index) => (
                  <TableRow key={session.id}>
                    <TableCell>#{index + 1}</TableCell>
                    <TableCell>{formatDate(session.sessionDate)}</TableCell>
                    <TableCell>
                      <Chip
                        color={getStatusColor(session.status)}
                        variant="flat"
                        size="sm"
                      >
                        {session.status}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      {session.bookingId ? `#${session.bookingId}` : '-'}
                    </TableCell>
                    <TableCell>
                      {session.notes || '-'}
                    </TableCell>
                    <TableCell>
                      {formatDate(session.createdAt)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardBody>
      </Card>
    </div>
  )
}
