'use client'

import { useState, useEffect, useCallback } from 'react'
import AdminLayout from '@/components/admin/AdminLayout'
import {
  Card,
  CardBody,
  Button,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Input,
  Textarea,
  Select,
  SelectItem,
  useDisclosure,
  Pagination,
} from "@heroui/react"
import { PlusIcon, EllipsisVerticalIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline'
import { useRouter } from 'next/navigation';
import { apiClient, Course } from '@/lib/api';
import { toast } from 'react-hot-toast';

interface Customer {
  id: number;
  displayName: string;
  email?: string;
}

export default function CourseManagement() {
  const router = useRouter();
  const [courses, setCourses] = useState<Course[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [, setTotal] = useState(0);
  const [submitting, setSubmitting] = useState(false);

  const { isOpen: isCreateOpen, onOpen: onCreateOpen, onClose: onCreateClose } = useDisclosure();
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure();
  const { isOpen: isExtendOpen, onOpen: onExtendOpen, onClose: onExtendClose } = useDisclosure();
  const { isOpen: isAddSessionsOpen, onOpen: onAddSessionsOpen, onClose: onAddSessionsClose } = useDisclosure();
  const { isOpen: isTransferOpen, onOpen: onTransferOpen, onClose: onTransferClose } = useDisclosure();

  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    totalSessions: '',
    startDate: '',
    expiryDate: '',
    customerId: '',
  });

  const [extendData, setExtendData] = useState({
    newExpiryDate: '',
  });

  const [addSessionsData, setAddSessionsData] = useState({
    additionalSessions: '',
    reason: '',
  });

  const [transferData, setTransferData] = useState({
    toCourseId: '',
    sessionCount: '',
    reason: '',
  });

  const loadCourses = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiClient.getAdminCourses({
        page: currentPage,
        limit: 10,
        search: searchTerm || undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined,
      });

      if (response.success) {
        setCourses(response.data.courses);
        setTotal(response.data.total);
        setTotalPages(Math.ceil(response.data.total / response.data.limit));
      }
    } catch (error) {
      console.error('Error loading courses:', error);
      toast.error('Failed to load courses');
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchTerm, statusFilter]);

  useEffect(() => {
    loadCourses();
  }, [searchTerm, statusFilter, currentPage, loadCourses]);

  useEffect(() => {
    loadCustomers();
  }, []);

  const loadCustomers = async () => {
    try {
      const response = await apiClient.getAdminUsers();
      if (response.success) {
        const customerData = response.data.users.map(user => ({
          id: user.id,
          displayName: user.displayName,
          email: user.email,
        }));
        setCustomers(customerData);
      }
    } catch (error) {
      console.error('Error loading customers:', error);
      toast.error('Failed to load customers');
    }
  };

  const handleCreateCourse = async () => {
    if (!formData.name || !formData.price || !formData.totalSessions || !formData.startDate || !formData.expiryDate || !formData.customerId) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setSubmitting(true);
      const response = await apiClient.createAdminCourse({
        name: formData.name,
        description: formData.description || undefined,
        price: parseFloat(formData.price),
        totalSessions: parseInt(formData.totalSessions),
        startDate: formData.startDate,
        expiryDate: formData.expiryDate,
        customerId: parseInt(formData.customerId),
      });

      if (response.success) {
        toast.success('Course created successfully');
        onCreateClose();
        resetForm();
        loadCourses();
      }
    } catch (error) {
      console.error('Error creating course:', error);
      toast.error('Failed to create course');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditCourse = (course: Course) => {
    setSelectedCourse(course);
    setFormData({
      name: course.name,
      description: course.description || '',
      price: course.price.toString(),
      totalSessions: course.totalSessions.toString(),
      startDate: course.startDate.split('T')[0],
      expiryDate: course.expiryDate.split('T')[0],
      customerId: course.customer.id.toString(),
    });
    onEditOpen();
  };

  const handleUpdateCourse = async () => {
    if (!selectedCourse || !formData.name || !formData.price || !formData.startDate || !formData.expiryDate) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setSubmitting(true);
      const response = await apiClient.updateAdminCourse(selectedCourse.id, {
        name: formData.name,
        description: formData.description || undefined,
        price: parseFloat(formData.price),
        startDate: formData.startDate,
        expiryDate: formData.expiryDate,
      });

      if (response.success) {
        toast.success('Course updated successfully');
        onEditClose();
        resetForm();
        loadCourses();
      }
    } catch (error) {
      console.error('Error updating course:', error);
      toast.error('Failed to update course');
    } finally {
      setSubmitting(false);
    }
  };

  const handleExtendExpiry = async () => {
    if (!selectedCourse || !extendData.newExpiryDate) {
      toast.error('Please select a new expiry date');
      return;
    }

    try {
      setSubmitting(true);
      const response = await apiClient.extendCourseExpiry(selectedCourse.id, extendData.newExpiryDate);

      if (response.success) {
        toast.success('Course expiry extended successfully');
        onExtendClose();
        setExtendData({ newExpiryDate: '' });
        loadCourses();
      }
    } catch (error) {
      console.error('Error extending course expiry:', error);
      toast.error('Failed to extend course expiry');
    } finally {
      setSubmitting(false);
    }
  };

  const handleAddSessions = async () => {
    if (!selectedCourse || !addSessionsData.additionalSessions || !addSessionsData.reason) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setSubmitting(true);
      const response = await apiClient.addCourseSessions(
        selectedCourse.id,
        parseInt(addSessionsData.additionalSessions),
        addSessionsData.reason
      );

      if (response.success) {
        toast.success('Sessions added successfully');
        onAddSessionsClose();
        setAddSessionsData({ additionalSessions: '', reason: '' });
        loadCourses();
      }
    } catch (error) {
      console.error('Error adding sessions:', error);
      toast.error('Failed to add sessions');
    } finally {
      setSubmitting(false);
    }
  };

  const handleTransferSessions = async () => {
    if (!selectedCourse || !transferData.toCourseId || !transferData.sessionCount || !transferData.reason) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setSubmitting(true);
      const response = await apiClient.transferCourseSessions(selectedCourse.id, {
        toCourseId: parseInt(transferData.toCourseId),
        sessionCount: parseInt(transferData.sessionCount),
        reason: transferData.reason,
      });

      if (response.success) {
        toast.success('Sessions transferred successfully');
        onTransferClose();
        setTransferData({ toCourseId: '', sessionCount: '', reason: '' });
        loadCourses();
      }
    } catch (error) {
      console.error('Error transferring sessions:', error);
      toast.error('Failed to transfer sessions');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteCourse = async (course: Course) => {
    if (!confirm(`Are you sure you want to delete the course "${course.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await apiClient.deleteCourse(course.id);

      if (response.success) {
        toast.success('Course deleted successfully');
        loadCourses();
      }
    } catch (error) {
      console.error('Error deleting course:', error);
      toast.error('Failed to delete course');
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      price: '',
      totalSessions: '',
      startDate: '',
      expiryDate: '',
      customerId: '',
    });
    setSelectedCourse(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'expired': return 'danger';
      case 'suspended': return 'warning';
      case 'transferred': return 'secondary';
      default: return 'default';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('th-TH');
  };

  return (
    <AdminLayout
      title="Course Management"
      subtitle="Manage customer courses and sessions"
    >
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="text-center space-y-4">
            <div className="w-12 h-12 border-4 border-[#5FCED3]/20 border-t-[#5FCED3] rounded-full animate-spin mx-auto"></div>
            <p className="text-slate-600 font-medium">Loading courses...</p>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <Button
              color="primary"
              startContent={<PlusIcon className="h-4 w-4" />}
              onPress={onCreateOpen}
            >
              Create Course
            </Button>
          </div>

          <Card>
            <CardBody>
              <div className="flex gap-4 items-end">
                <Input
                  placeholder="Search courses or customers..."
                  value={searchTerm}
                  onValueChange={setSearchTerm}
                  startContent={<MagnifyingGlassIcon className="h-4 w-4" />}
                  className="flex-1"
                />
                <Select
                  placeholder="Filter by status"
                  selectedKeys={statusFilter !== 'all' ? [statusFilter] : []}
                  onSelectionChange={(keys) => {
                    const selected = Array.from(keys)[0] as string;
                    setStatusFilter(selected || 'all');
                  }}
                  className="w-48"
                >
                  <SelectItem key="all">All Status</SelectItem>
                  <SelectItem key="active">Active</SelectItem>
                  <SelectItem key="expired">Expired</SelectItem>
                  <SelectItem key="suspended">Suspended</SelectItem>
                  <SelectItem key="transferred">Transferred</SelectItem>
                </Select>
              </div>
            </CardBody>
          </Card>

          {/* Courses Table */}
          <Card>
            <CardBody className="p-0">
              <Table aria-label="Courses table">
                <TableHeader>
                  <TableColumn>COURSE</TableColumn>
                  <TableColumn>CUSTOMER</TableColumn>
                  <TableColumn>SESSIONS</TableColumn>
                  <TableColumn>PRICE</TableColumn>
                  <TableColumn>EXPIRY</TableColumn>
                  <TableColumn>STATUS</TableColumn>
                  <TableColumn>ACTIONS</TableColumn>
                </TableHeader>
                <TableBody emptyContent="No courses found">
                  {courses.map((course) => (
                    <TableRow key={course.id}>
                      <TableCell>
                        <div>
                          <p className="font-semibold">{course.name}</p>
                          {course.description && (
                            <p className="text-sm text-default-500">{course.description}</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{course.customer.displayName}</p>
                          {course.customer.email && (
                            <p className="text-sm text-default-500">{course.customer.email}</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <p>Used: {course.usedSessions}/{course.totalSessions}</p>
                          <p className="text-default-500">Remaining: {course.remainingSessions}</p>
                        </div>
                      </TableCell>
                      <TableCell>฿{course.price.toLocaleString()}</TableCell>
                      <TableCell>{formatDate(course.expiryDate)}</TableCell>
                      <TableCell>
                        <Chip
                          color={getStatusColor(course.status)}
                          variant="flat"
                          size="sm"
                        >
                          {course.status}
                        </Chip>
                      </TableCell>
                      <TableCell>
                        <Dropdown>
                          <DropdownTrigger>
                            <Button isIconOnly size="sm" variant="light">
                              <EllipsisVerticalIcon className="h-4 w-4" />
                            </Button>
                          </DropdownTrigger>
                          <DropdownMenu>
                            <DropdownItem key="edit" onPress={() => handleEditCourse(course)}>
                              Edit Course
                            </DropdownItem>
                            <DropdownItem key="sessions" onPress={() => router.push(`/admin/courses/${course.id}/sessions`)}>
                              View Sessions
                            </DropdownItem>
                            <DropdownItem key="extend" onPress={() => {
                              setSelectedCourse(course);
                              setExtendData({ newExpiryDate: course.expiryDate.split('T')[0] });
                              onExtendOpen();
                            }}>
                              Extend Expiry
                            </DropdownItem>
                            <DropdownItem key="add-sessions" onPress={() => {
                              setSelectedCourse(course);
                              setAddSessionsData({ additionalSessions: '', reason: '' });
                              onAddSessionsOpen();
                            }}>
                              Add Sessions
                            </DropdownItem>
                            <DropdownItem key="transfer" onPress={() => {
                              setSelectedCourse(course);
                              setTransferData({ toCourseId: '', sessionCount: '', reason: '' });
                              onTransferOpen();
                            }}>
                              Transfer Sessions
                            </DropdownItem>
                            <DropdownItem
                              key="delete"
                              className="text-danger"
                              color="danger"
                              onPress={() => handleDeleteCourse(course)}
                            >
                              Delete Course
                            </DropdownItem>
                          </DropdownMenu>
                        </Dropdown>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardBody>
          </Card>

          {/* Pagination */}
          {
            totalPages > 1 && (
              <div className="flex justify-center">
                <Pagination
                  total={totalPages}
                  page={currentPage}
                  onChange={setCurrentPage}
                  showControls
                />
              </div>
            )
          }

          {/* Create Course Modal */}
          <Modal isOpen={isCreateOpen} onClose={onCreateClose} size="2xl">
            <ModalContent>
              <ModalHeader>Create New Course</ModalHeader>
              <ModalBody>
                <div className="space-y-4">
                  <Input
                    label="Course Name"
                    placeholder="Enter course name"
                    value={formData.name}
                    onValueChange={(value) => setFormData({ ...formData, name: value })}
                    isRequired
                  />
                  <Textarea
                    label="Description"
                    placeholder="Enter course description"
                    value={formData.description}
                    onValueChange={(value) => setFormData({ ...formData, description: value })}
                  />
                  <div className="grid grid-cols-2 gap-4">
                    <Input
                      label="Price (฿)"
                      type="number"
                      placeholder="0"
                      value={formData.price}
                      onValueChange={(value) => setFormData({ ...formData, price: value })}
                      isRequired
                    />
                    <Input
                      label="Total Sessions"
                      type="number"
                      placeholder="0"
                      value={formData.totalSessions}
                      onValueChange={(value) => setFormData({ ...formData, totalSessions: value })}
                      isRequired
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <Input
                      label="Start Date"
                      type="date"
                      value={formData.startDate}
                      onValueChange={(value) => setFormData({ ...formData, startDate: value })}
                      isRequired
                    />
                    <Input
                      label="Expiry Date"
                      type="date"
                      value={formData.expiryDate}
                      onValueChange={(value) => setFormData({ ...formData, expiryDate: value })}
                      isRequired
                    />
                  </div>
                  <Select
                    label="Customer"
                    placeholder="Select customer"
                    selectedKeys={formData.customerId ? [formData.customerId] : []}
                    onSelectionChange={(keys) => {
                      const selected = Array.from(keys)[0] as string;
                      setFormData({ ...formData, customerId: selected || '' });
                    }}
                    isRequired
                  >
                    {customers.map((customer) => (
                      <SelectItem key={customer.id}>
                        {customer.displayName}
                      </SelectItem>
                    ))}
                  </Select>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onCreateClose} isDisabled={submitting}>
                  Cancel
                </Button>
                <Button color="primary" onPress={handleCreateCourse} isLoading={submitting}>
                  Create Course
                </Button>
              </ModalFooter>
            </ModalContent>
          </Modal>

          {/* Edit Course Modal */}
          <Modal isOpen={isEditOpen} onClose={onEditClose} size="2xl">
            <ModalContent>
              <ModalHeader>Edit Course</ModalHeader>
              <ModalBody>
                <div className="space-y-4">
                  <Input
                    label="Course Name"
                    placeholder="Enter course name"
                    value={formData.name}
                    onValueChange={(value) => setFormData({ ...formData, name: value })}
                    isRequired
                  />
                  <Textarea
                    label="Description"
                    placeholder="Enter course description"
                    value={formData.description}
                    onValueChange={(value) => setFormData({ ...formData, description: value })}
                  />
                  <div className="grid grid-cols-2 gap-4">
                    <Input
                      label="Price (฿)"
                      type="number"
                      placeholder="0"
                      value={formData.price}
                      onValueChange={(value) => setFormData({ ...formData, price: value })}
                      isRequired
                    />
                    <Input
                      label="Total Sessions"
                      type="number"
                      placeholder="0"
                      value={formData.totalSessions}
                      isDisabled
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <Input
                      label="Start Date"
                      type="date"
                      value={formData.startDate}
                      onValueChange={(value) => setFormData({ ...formData, startDate: value })}
                      isRequired
                    />
                    <Input
                      label="Expiry Date"
                      type="date"
                      value={formData.expiryDate}
                      onValueChange={(value) => setFormData({ ...formData, expiryDate: value })}
                      isRequired
                    />
                  </div>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onEditClose} isDisabled={submitting}>
                  Cancel
                </Button>
                <Button color="primary" onPress={handleUpdateCourse} isLoading={submitting}>
                  Update Course
                </Button>
              </ModalFooter>
            </ModalContent>
          </Modal>

          {/* Extend Expiry Modal */}
          <Modal isOpen={isExtendOpen} onClose={onExtendClose}>
            <ModalContent>
              <ModalHeader>Extend Course Expiry</ModalHeader>
              <ModalBody>
                <div className="space-y-4">
                  <p>Extending expiry for: <strong>{selectedCourse?.name}</strong></p>
                  <Input
                    label="New Expiry Date"
                    type="date"
                    value={extendData.newExpiryDate}
                    onValueChange={(value) => setExtendData({ newExpiryDate: value })}
                    isRequired
                  />
                </div>
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onExtendClose} isDisabled={submitting}>
                  Cancel
                </Button>
                <Button color="primary" onPress={handleExtendExpiry} isLoading={submitting}>
                  Extend Expiry
                </Button>
              </ModalFooter>
            </ModalContent>
          </Modal>

          {/* Add Sessions Modal */}
          <Modal isOpen={isAddSessionsOpen} onClose={onAddSessionsClose}>
            <ModalContent>
              <ModalHeader>Add Sessions</ModalHeader>
              <ModalBody>
                <div className="space-y-4">
                  <p>Adding sessions to: <strong>{selectedCourse?.name}</strong></p>
                  <Input
                    label="Additional Sessions"
                    type="number"
                    placeholder="0"
                    value={addSessionsData.additionalSessions}
                    onValueChange={(value) => setAddSessionsData({ ...addSessionsData, additionalSessions: value })}
                    isRequired
                  />
                  <Textarea
                    label="Reason"
                    placeholder="Enter reason for adding sessions"
                    value={addSessionsData.reason}
                    onValueChange={(value) => setAddSessionsData({ ...addSessionsData, reason: value })}
                    isRequired
                  />
                </div>
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onAddSessionsClose} isDisabled={submitting}>
                  Cancel
                </Button>
                <Button color="primary" onPress={handleAddSessions} isLoading={submitting}>
                  Add Sessions
                </Button>
              </ModalFooter>
            </ModalContent>
          </Modal>

          {/* Transfer Sessions Modal */}
          <Modal isOpen={isTransferOpen} onClose={onTransferClose}>
            <ModalContent>
              <ModalHeader>Transfer Sessions</ModalHeader>
              <ModalBody>
                <div className="space-y-4">
                  <p>Transferring sessions from: <strong>{selectedCourse?.name}</strong></p>
                  <Select
                    label="To Course"
                    placeholder="Select destination course"
                    selectedKeys={transferData.toCourseId ? [transferData.toCourseId] : []}
                    onSelectionChange={(keys) => {
                      const selected = Array.from(keys)[0] as string;
                      setTransferData({ ...transferData, toCourseId: selected || '' });
                    }}
                    isRequired
                  >
                    {courses.filter(c => c.id !== selectedCourse?.id).map((course) => (
                      <SelectItem key={course.id.toString()}>
                        {course.name} - {course.customer.displayName}
                      </SelectItem>
                    ))}
                  </Select>
                  <Input
                    label="Sessions to Transfer"
                    type="number"
                    placeholder="0"
                    value={transferData.sessionCount}
                    onValueChange={(value) => setTransferData({ ...transferData, sessionCount: value })}
                    isRequired
                  />
                  <Textarea
                    label="Reason"
                    placeholder="Enter reason for transfer"
                    value={transferData.reason}
                    onValueChange={(value) => setTransferData({ ...transferData, reason: value })}
                    isRequired
                  />
                </div>
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onTransferClose} isDisabled={submitting}>
                  Cancel
                </Button>
                <Button color="primary" onPress={handleTransferSessions} isLoading={submitting}>
                  Transfer Sessions
                </Button>
              </ModalFooter>
            </ModalContent>
          </Modal>
        </div>
      )}
    </AdminLayout>
  );
}