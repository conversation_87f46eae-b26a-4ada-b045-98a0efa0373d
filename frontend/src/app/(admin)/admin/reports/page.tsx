'use client'

import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import {
  Card,
  CardBody,
  CardHeader,
  Select,
  SelectItem,
  Chip
} from "@heroui/react";

export default function ReportsAnalytics() {
  const [timeRange, setTimeRange] = useState('7days');
  const [, setLoading] = useState(true);

  // Mock data for demonstration
  const [analytics] = useState({
    revenue: {
      total: 45600,
      growth: 12.5,
      trend: 'up'
    },
    bookings: {
      total: 156,
      growth: 8.3,
      trend: 'up'
    },
    customers: {
      total: 89,
      growth: 15.2,
      trend: 'up'
    },
    classes: {
      total: 24,
      growth: -2.1,
      trend: 'down'
    }
  });

  const [popularClasses] = useState([
    { name: 'Beginner Pilates', bookings: 45, revenue: 22500 },
    { name: 'Intermediate Pilates', bookings: 38, revenue: 22800 },
    { name: '<PERSON> Pilates', bookings: 42, revenue: 18900 },
    { name: 'Advanced Pilates', bookings: 31, revenue: 21700 }
  ]);

  const [recentActivity] = useState([
    { type: 'booking', message: 'New booking: <PERSON> Pilates', time: '2 min ago' },
    { type: 'payment', message: 'Payment received: ฿500 from <PERSON>', time: '15 min ago' },
    { type: 'cancellation', message: 'Booking cancelled: Sarah Wilson - Mat Pilates', time: '1 hour ago' },
    { type: 'registration', message: 'New customer registered: David Brown', time: '2 hours ago' }
  ]);

  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, [timeRange]);

  const getGrowthColor = (trend: string) => {
    return trend === 'up' ? 'success' : 'danger';
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'booking': return '📅';
      case 'payment': return '💰';
      case 'cancellation': return '❌';
      case 'registration': return '👤';
      default: return '📊';
    }
  };

  return (
    <AdminLayout
      title="Reports & Analytics"
      subtitle="Track your studio's performance and insights"
    >
      <div className="space-y-6">
        {/* Time Range Filter */}
        <div className="flex justify-end mb-6">
          <Select
            label="Time Range"
            selectedKeys={[timeRange]}
            onSelectionChange={(keys) => setTimeRange(Array.from(keys)[0] as string)}
            className="w-48"
          >
            <SelectItem key="7days">Last 7 Days</SelectItem>
            <SelectItem key="30days">Last 30 Days</SelectItem>
            <SelectItem key="90days">Last 90 Days</SelectItem>
            <SelectItem key="1year">Last Year</SelectItem>
          </Select>
        </div>
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardBody>
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-small text-default-600">Total Revenue</p>
                  <p className="text-2xl font-bold">฿{analytics.revenue.total.toLocaleString()}</p>
                </div>
                <Chip
                  color={getGrowthColor(analytics.revenue.trend)}
                  size="sm"
                  variant="flat"
                >
                  {analytics.revenue.trend === 'up' ? '+' : ''}{analytics.revenue.growth}%
                </Chip>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-small text-default-600">Total Bookings</p>
                  <p className="text-2xl font-bold">{analytics.bookings.total}</p>
                </div>
                <Chip
                  color={getGrowthColor(analytics.bookings.trend)}
                  size="sm"
                  variant="flat"
                >
                  {analytics.bookings.trend === 'up' ? '+' : ''}{analytics.bookings.growth}%
                </Chip>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-small text-default-600">Active Customers</p>
                  <p className="text-2xl font-bold">{analytics.customers.total}</p>
                </div>
                <Chip
                  color={getGrowthColor(analytics.customers.trend)}
                  size="sm"
                  variant="flat"
                >
                  {analytics.customers.trend === 'up' ? '+' : ''}{analytics.customers.growth}%
                </Chip>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-small text-default-600">Classes Offered</p>
                  <p className="text-2xl font-bold">{analytics.classes.total}</p>
                </div>
                <Chip
                  color={getGrowthColor(analytics.classes.trend)}
                  size="sm"
                  variant="flat"
                >
                  {analytics.classes.trend === 'up' ? '+' : ''}{analytics.classes.growth}%
                </Chip>
              </div>
            </CardBody>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Popular Classes */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Popular Classes</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                {popularClasses.map((classItem, index) => (
                  <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">{classItem.name}</p>
                      <p className="text-small text-default-500">{classItem.bookings} bookings</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">฿{classItem.revenue.toLocaleString()}</p>
                      <p className="text-small text-default-500">revenue</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Recent Activity</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg">{getActivityIcon(activity.type)}</div>
                    <div className="flex-1">
                      <p className="text-sm">{activity.message}</p>
                      <p className="text-xs text-default-500">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Revenue Chart Placeholder */}
        <Card className="mt-8">
          <CardHeader>
            <h3 className="text-lg font-semibold">Revenue Trend</h3>
          </CardHeader>
          <CardBody>
            <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <p className="text-lg font-medium text-default-600">Revenue Chart</p>
                <p className="text-small text-default-500">Chart visualization would go here</p>
                <p className="text-small text-default-400 mt-2">
                  Integration with charting library (Chart.js, Recharts, etc.)
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Booking Trends */}
        <Card className="mt-8">
          <CardHeader>
            <h3 className="text-lg font-semibold">Booking Trends</h3>
          </CardHeader>
          <CardBody>
            <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <p className="text-lg font-medium text-default-600">Booking Trends Chart</p>
                <p className="text-small text-default-500">Booking patterns and trends would be displayed here</p>
                <p className="text-small text-default-400 mt-2">
                  Shows peak hours, popular days, seasonal trends
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </AdminLayout>
  );
}
