"use client";

import React, { useState, useEffect } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import {
  Card,
  CardBody,
  CardHeader,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Button,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Input,
  Select,
  SelectItem,
  Spinner,
  useDisclosure,
  Tooltip,
} from "@heroui/react";
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Shield,
  Users,
  Building2,
  Mail,
  Phone,
} from "lucide-react";
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import { useRouter } from 'next/navigation';
import { apiClient, Admin, CreateAdminDto, UpdateAdminDto, Branch } from '@/lib/api';

export default function AdminUsersPage() {
  const { admin } = useAdminAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [selectedAdmin, setSelectedAdmin] = useState<Admin | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    isOpen: isCreateOpen,
    onOpen: onCreateOpen,
    onClose: onCreateClose,
  } = useDisclosure();

  const {
    isOpen: isEditOpen,
    onOpen: onEditOpen,
    onClose: onEditClose,
  } = useDisclosure();

  const {
    isOpen: isViewOpen,
    onOpen: onViewOpen,
    onClose: onViewClose,
  } = useDisclosure();

  const [createForm, setCreateForm] = useState<CreateAdminDto>({
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
    role: 'branch_admin',
    branchId: undefined,
  });

  const [editForm, setEditForm] = useState<UpdateAdminDto>({
    email: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
    role: 'branch_admin',
    branchId: undefined,
  });

  useEffect(() => {
    // Redirect if not super admin
    if (admin && admin.role !== 'super_admin') {
      router.push('/admin');
      return;
    }

    loadData();
  }, [admin, router]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [adminsResponse, branchesResponse] = await Promise.all([
        apiClient.getAllAdmins(),
        apiClient.getBranches(),
      ]);

      if (adminsResponse.success) {
        setAdmins(adminsResponse.data);
      }

      if (branchesResponse.success) {
        setBranches(branchesResponse.data.branches);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      console.error('Failed to load admin users');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAdmin = async () => {
    try {
      setIsSubmitting(true);
      const response = await apiClient.createAdmin(createForm);

      if (response.success) {
        console.log('Admin created successfully');
        setAdmins([response.data, ...admins]);
        onCreateClose();
        resetCreateForm();
      }
    } catch (error: unknown) {
      console.error('Error creating admin:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create admin';
      console.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditAdmin = async () => {
    if (!selectedAdmin) return;

    try {
      setIsSubmitting(true);
      const response = await apiClient.updateAdmin(selectedAdmin.id, editForm);

      if (response.success) {
        console.log('Admin updated successfully');
        setAdmins(admins.map(a => a.id === selectedAdmin.id ? response.data : a));
        onEditClose();
        setSelectedAdmin(null);
      }
    } catch (error: any) {
      console.error('Error updating admin:', error);
      console.error(error.response?.data?.message || 'Failed to update admin');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeactivateAdmin = async (adminId: number) => {
    if (!confirm('Are you sure you want to deactivate this admin?')) return;

    try {
      const response = await apiClient.deactivateAdmin(adminId);

      if (response.success) {
        console.log('Admin deactivated successfully');
        setAdmins(admins.map(a => a.id === adminId ? response.data : a));
      }
    } catch (error: any) {
      console.error('Error deactivating admin:', error);
      console.error(error.response?.data?.message || 'Failed to deactivate admin');
    }
  };

  const resetCreateForm = () => {
    setCreateForm({
      email: '',
      password: '',
      firstName: '',
      lastName: '',
      phoneNumber: '',
      role: 'branch_admin',
      branchId: undefined,
    });
  };

  const openEditModal = (admin: Admin) => {
    setSelectedAdmin(admin);
    setEditForm({
      email: admin.email,
      firstName: admin.firstName,
      lastName: admin.lastName,
      phoneNumber: admin.phoneNumber || '',
      role: admin.role,
      branchId: admin.branch?.id,
    });
    onEditOpen();
  };

  const openViewModal = (admin: Admin) => {
    setSelectedAdmin(admin);
    onViewOpen();
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'danger';
      case 'branch_admin':
        return 'primary';
      default:
        return 'default';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'super_admin':
        return <Shield className="w-4 h-4" />;
      case 'branch_admin':
        return <Building2 className="w-4 h-4" />;
      default:
        return <Users className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <AdminLayout title="Admin User Management" subtitle="Manage system administrators">
        <div className="flex justify-center items-center h-64">
          <Spinner size="lg" color="primary" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Admin User Management"
      subtitle="Manage system administrators and their permissions"
    >
      <div className="space-y-6">
        {/* Header with Create Button */}
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-[#5FCED3]/10 rounded-xl">
              <Shield className="w-8 h-8 text-[#5FCED3]" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-slate-800">Admin Users</h2>
              <p className="text-slate-600">Total: {admins.length} administrators</p>
            </div>
          </div>
          <Button
            color="primary"
            startContent={<Plus className="w-4 h-4" />}
            onPress={onCreateOpen}
            className="bg-[#5FCED3] hover:bg-[#5FCED3]/90"
          >
            Create Admin
          </Button>
        </div>

        {/* Admin Table */}
        <Card className="bg-white border-0 shadow-lg">
          <CardHeader className="pb-4">
            <h3 className="text-xl font-semibold text-slate-800">
              Administrator List
            </h3>
          </CardHeader>
          <CardBody className="pt-0">
            <Table aria-label="Admin users table">
              <TableHeader>
                <TableColumn>ADMIN</TableColumn>
                <TableColumn>ROLE</TableColumn>
                <TableColumn>BRANCH</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>LAST LOGIN</TableColumn>
                <TableColumn>ACTIONS</TableColumn>
              </TableHeader>
              <TableBody>
                {admins.map((adminUser) => (
                  <TableRow key={adminUser.id}>
                    <TableCell>
                      <div className="flex flex-col">
                        <p className="font-medium text-slate-800">
                          {adminUser.fullName}
                        </p>
                        <p className="text-sm text-slate-500">
                          {adminUser.email}
                        </p>
                        {adminUser.phoneNumber && (
                          <p className="text-xs text-slate-400">
                            {adminUser.phoneNumber}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Chip
                        size="sm"
                        color={getRoleColor(adminUser.role)}
                        variant="flat"
                        startContent={getRoleIcon(adminUser.role)}
                      >
                        {adminUser.role.replace('_', ' ').toUpperCase()}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      {adminUser.branch ? (
                        <div className="flex flex-col">
                          <p className="font-medium text-slate-700">
                            {adminUser.branch.name}
                          </p>
                          <p className="text-xs text-slate-500">
                            {adminUser.branch.address}
                          </p>
                        </div>
                      ) : (
                        <span className="text-slate-400">All Branches</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Chip
                        size="sm"
                        color={adminUser.isActive ? 'success' : 'danger'}
                        variant="flat"
                      >
                        {adminUser.isActive ? 'Active' : 'Inactive'}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-slate-600">
                        {formatDate(adminUser.lastLoginAt)}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Tooltip content="View Details">
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            onPress={() => openViewModal(adminUser)}
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                        </Tooltip>
                        <Tooltip content="Edit Admin">
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            onPress={() => openEditModal(adminUser)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                        </Tooltip>
                        {adminUser.isActive && adminUser.id !== admin?.id && (
                          <Tooltip content="Deactivate Admin">
                            <Button
                              isIconOnly
                              size="sm"
                              variant="light"
                              color="danger"
                              onPress={() => handleDeactivateAdmin(adminUser.id)}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </Tooltip>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardBody>
        </Card>

        {/* Create Admin Modal */}
        <Modal
          isOpen={isCreateOpen}
          onClose={onCreateClose}
          size="2xl"
          scrollBehavior="inside"
        >
          <ModalContent>
            <ModalHeader className="flex flex-col gap-1">
              <h3 className="text-xl font-semibold">Create New Admin</h3>
              <p className="text-sm text-slate-600">Add a new administrator to the system</p>
            </ModalHeader>
            <ModalBody>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="First Name"
                    placeholder="Enter first name"
                    value={createForm.firstName}
                    onChange={(e) => setCreateForm({ ...createForm, firstName: e.target.value })}
                    isRequired
                    variant="bordered"
                  />
                  <Input
                    label="Last Name"
                    placeholder="Enter last name"
                    value={createForm.lastName}
                    onChange={(e) => setCreateForm({ ...createForm, lastName: e.target.value })}
                    isRequired
                    variant="bordered"
                  />
                </div>
                <Input
                  label="Email"
                  placeholder="Enter email address"
                  type="email"
                  value={createForm.email}
                  onChange={(e) => setCreateForm({ ...createForm, email: e.target.value })}
                  isRequired
                  variant="bordered"
                  startContent={<Mail className="w-4 h-4 text-default-400" />}
                />
                <Input
                  label="Password"
                  placeholder="Enter password (min 6 characters)"
                  type="password"
                  value={createForm.password}
                  onChange={(e) => setCreateForm({ ...createForm, password: e.target.value })}
                  isRequired
                  variant="bordered"
                  minLength={6}
                />
                <Input
                  label="Phone Number"
                  placeholder="Enter phone number (optional)"
                  value={createForm.phoneNumber}
                  onChange={(e) => setCreateForm({ ...createForm, phoneNumber: e.target.value })}
                  variant="bordered"
                  startContent={<Phone className="w-4 h-4 text-default-400" />}
                />
                <Select
                  label="Role"
                  placeholder="Select admin role"
                  selectedKeys={createForm.role ? [createForm.role] : []}
                  onSelectionChange={(keys) => {
                    const selected = Array.from(keys)[0] as 'super_admin' | 'branch_admin';
                    setCreateForm({ ...createForm, role: selected, branchId: selected === 'super_admin' ? undefined : createForm.branchId });
                  }}
                  isRequired
                  variant="bordered"
                  startContent={<Shield className="w-4 h-4 text-default-400" />}
                >
                  <SelectItem key="super_admin">Super Admin</SelectItem>
                  <SelectItem key="branch_admin">Branch Admin</SelectItem>
                </Select>
                {createForm.role === 'branch_admin' && (
                  <Select
                    label="Assigned Branch"
                    placeholder="Select branch for admin"
                    selectedKeys={createForm.branchId ? [createForm.branchId.toString()] : []}
                    onSelectionChange={(keys) => {
                      const selected = Array.from(keys)[0] as string;
                      setCreateForm({ ...createForm, branchId: selected ? parseInt(selected) : undefined });
                    }}
                    isRequired
                    variant="bordered"
                    startContent={<Building2 className="w-4 h-4 text-default-400" />}
                  >
                    {branches.map((branch) => (
                      <SelectItem key={branch.id.toString()}>
                        {branch.name}
                      </SelectItem>
                    ))}
                  </Select>
                )}
              </div>
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={onCreateClose}>
                Cancel
              </Button>
              <Button
                color="primary"
                onPress={handleCreateAdmin}
                isLoading={isSubmitting}
                className="bg-[#5FCED3] hover:bg-[#5FCED3]/90"
              >
                Create Admin
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* Edit Admin Modal */}
        <Modal
          isOpen={isEditOpen}
          onClose={onEditClose}
          size="2xl"
          scrollBehavior="inside"
        >
          <ModalContent>
            <ModalHeader className="flex flex-col gap-1">
              <h3 className="text-xl font-semibold">Edit Admin</h3>
              <p className="text-sm text-slate-600">Update administrator information</p>
            </ModalHeader>
            <ModalBody>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="First Name"
                    placeholder="Enter first name"
                    value={editForm.firstName}
                    onChange={(e) => setEditForm({ ...editForm, firstName: e.target.value })}
                    isRequired
                    variant="bordered"
                  />
                  <Input
                    label="Last Name"
                    placeholder="Enter last name"
                    value={editForm.lastName}
                    onChange={(e) => setEditForm({ ...editForm, lastName: e.target.value })}
                    isRequired
                    variant="bordered"
                  />
                </div>
                <Input
                  label="Email"
                  placeholder="Enter email address"
                  type="email"
                  value={editForm.email}
                  onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                  isRequired
                  variant="bordered"
                  startContent={<Mail className="w-4 h-4 text-default-400" />}
                />
                <Input
                  label="Phone Number"
                  placeholder="Enter phone number (optional)"
                  value={editForm.phoneNumber}
                  onChange={(e) => setEditForm({ ...editForm, phoneNumber: e.target.value })}
                  variant="bordered"
                  startContent={<Phone className="w-4 h-4 text-default-400" />}
                />
                <Select
                  label="Role"
                  placeholder="Select admin role"
                  selectedKeys={editForm.role ? [editForm.role] : []}
                  onSelectionChange={(keys) => {
                    const selected = Array.from(keys)[0] as 'super_admin' | 'branch_admin';
                    setEditForm({ ...editForm, role: selected, branchId: selected === 'super_admin' ? undefined : editForm.branchId });
                  }}
                  isRequired
                  variant="bordered"
                  startContent={<Shield className="w-4 h-4 text-default-400" />}
                >
                  <SelectItem key="super_admin">Super Admin</SelectItem>
                  <SelectItem key="branch_admin">Branch Admin</SelectItem>
                </Select>
                {editForm.role === 'branch_admin' && (
                  <Select
                    label="Assigned Branch"
                    placeholder="Select branch for admin"
                    selectedKeys={editForm.branchId ? [editForm.branchId.toString()] : []}
                    onSelectionChange={(keys) => {
                      const selected = Array.from(keys)[0] as string;
                      setEditForm({ ...editForm, branchId: selected ? parseInt(selected) : undefined });
                    }}
                    isRequired
                    variant="bordered"
                    startContent={<Building2 className="w-4 h-4 text-default-400" />}
                  >
                    {branches.map((branch) => (
                      <SelectItem key={branch.id.toString()}>
                        {branch.name}
                      </SelectItem>
                    ))}
                  </Select>
                )}
              </div>
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={onEditClose}>
                Cancel
              </Button>
              <Button
                color="primary"
                onPress={handleEditAdmin}
                isLoading={isSubmitting}
                className="bg-[#5FCED3] hover:bg-[#5FCED3]/90"
              >
                Update Admin
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* View Admin Modal */}
        <Modal
          isOpen={isViewOpen}
          onClose={onViewClose}
          size="lg"
        >
          <ModalContent>
            <ModalHeader className="flex flex-col gap-1">
              <h3 className="text-xl font-semibold">Admin Details</h3>
              <p className="text-sm text-slate-600">View administrator information</p>
            </ModalHeader>
            <ModalBody>
              {selectedAdmin && (
                <div className="space-y-6">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-[#5FCED3]/10 rounded-xl">
                      {getRoleIcon(selectedAdmin.role)}
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-slate-800">
                        {selectedAdmin.fullName}
                      </h4>
                      <Chip
                        size="sm"
                        color={getRoleColor(selectedAdmin.role)}
                        variant="flat"
                      >
                        {selectedAdmin.role.replace('_', ' ').toUpperCase()}
                      </Chip>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-slate-600">Email</label>
                      <p className="text-slate-800">{selectedAdmin.email}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-slate-600">Phone</label>
                      <p className="text-slate-800">{selectedAdmin.phoneNumber || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-slate-600">Status</label>
                      <div className="mt-1">
                        <Chip
                          size="sm"
                          color={selectedAdmin.isActive ? 'success' : 'danger'}
                          variant="flat"
                        >
                          {selectedAdmin.isActive ? 'Active' : 'Inactive'}
                        </Chip>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-slate-600">Last Login</label>
                      <p className="text-slate-800">{formatDate(selectedAdmin.lastLoginAt)}</p>
                    </div>
                  </div>

                  {selectedAdmin.branch && (
                    <div>
                      <label className="text-sm font-medium text-slate-600">Assigned Branch</label>
                      <div className="mt-2 p-3 bg-slate-50 rounded-lg">
                        <p className="font-medium text-slate-800">{selectedAdmin.branch.name}</p>
                        <p className="text-sm text-slate-600">{selectedAdmin.branch.address}</p>
                      </div>
                    </div>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-slate-600">Created At</label>
                      <p className="text-slate-800">{formatDate(selectedAdmin.createdAt)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-slate-600">Updated At</label>
                      <p className="text-slate-800">{formatDate(selectedAdmin.updatedAt)}</p>
                    </div>
                  </div>
                </div>
              )}
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={onViewClose}>
                Close
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    </AdminLayout>
  );
}
