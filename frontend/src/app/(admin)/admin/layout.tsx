'use client'

import { useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAdminAuth, AdminAuthProvider } from '@/contexts/AdminAuthContext'
import { Card, CardBody, Button } from '@heroui/react'

function AdminLayoutInner({
  children,
}: {
  children: React.ReactNode
}) {
  const { admin, loading } = useAdminAuth()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // Don't redirect if we're already on the login page
    if (pathname === '/admin/login') return

    // Redirect to login if not authenticated
    if (!loading && !admin) {
      router.push('/admin/login')
      return
    }
  }, [loading, admin, router, pathname])

  // Show loading spinner
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex items-center justify-center">
        <div className="text-center space-y-4">
          {/* Elegant Spinner */}
          <div className="relative">
            <div className="w-16 h-16 border-4 border-gray-100 rounded-full animate-spin">
              <div className="absolute inset-0 border-4 border-transparent border-t-[#5FCED3] rounded-full animate-spin"></div>
            </div>
            {/* Pulsing center dot */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-2 h-2 bg-[#5FCED3] rounded-full animate-pulse"></div>
            </div>
          </div>

          {/* Loading message */}
          <div className="space-y-2">
            <p className="text-slate-700 font-medium text-xl">Loading Admin Portal...</p>
            <div className="flex justify-center space-x-1">
              <div className="w-2 h-2 bg-[#5FCED3] rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 bg-[#5FCED3] rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 bg-[#5FCED3] rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Allow login page to render without authentication
  if (pathname === '/admin/login') {
    return children
  }

  // Show login prompt if not authenticated
  if (!admin) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="max-w-md">
          <CardBody className="text-center space-y-4">
            <h1 className="text-2xl font-bold">Admin Access Required</h1>
            <p className="text-default-600">
              Please log in with an admin account to access the admin panel.
            </p>
            <Button
              color="primary"
              onPress={() => router.push('/admin/login')}
            >
              Go to Login
            </Button>
          </CardBody>
        </Card>
      </div>
    )
  }

  // Render admin interface for authenticated admin users
  return (
    <div className="min-h-screen bg-background">
      {/* Desktop-optimized layout */}
      <div className="">
        {children}
      </div>
    </div>
  )
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <AdminAuthProvider>
      <AdminLayoutInner>{children}</AdminLayoutInner>
    </AdminAuthProvider>
  )
}
