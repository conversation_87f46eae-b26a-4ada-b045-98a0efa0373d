"use client";

import React, { useState, useEffect } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import {
  <PERSON>,
  Card<PERSON>ody,
  Card<PERSON><PERSON>er,
  <PERSON>,
  <PERSON><PERSON>,
  Spinner,
} from "@heroui/react";
import {
  Users,
  GraduationCap,
  Calendar,
  DollarSign,
  Activity,
  ArrowUpRight,
  Eye,
  MapPin,
  BarChart3,
  TrendingUp,
  TrendingDown,
} from "lucide-react";
import Link from "next/link";
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import { useRouter } from 'next/navigation';

interface BranchStats {
  totalCustomers: number;
  activeCustomers: number;
  totalCourses: number;
  activeCourses: number;
  monthlyRevenue: number;
  revenueGrowth: number;
  activeBookings: number;
  todaySessions: number;
}

interface RecentActivity {
  id: number;
  type: 'customer' | 'booking' | 'course' | 'payment';
  message: string;
  time: string;
  status: 'success' | 'warning' | 'info' | 'danger';
}

const mockStats: BranchStats = {
  totalCustomers: 320,
  activeCustomers: 245,
  totalCourses: 28,
  activeCourses: 24,
  monthlyRevenue: 145000,
  revenueGrowth: 15.2,
  activeBookings: 45,
  todaySessions: 12,
};

const quickActions = [
  {
    title: "Customer Management",
    description: "Manage customer accounts and profiles",
    icon: <Users className="w-6 h-6" />,
    href: "/admin/customers",
    gradient: "from-blue-500 to-blue-600",
  },
  {
    title: "Course Management",
    description: "Create and manage courses",
    icon: <GraduationCap className="w-6 h-6" />,
    href: "/admin/courses",
    gradient: "from-[#5FCED3] to-[#5FCED3]/80",
  },
  {
    title: "Booking Management",
    description: "View and manage bookings",
    icon: <Calendar className="w-6 h-6" />,
    href: "/admin/bookings",
    gradient: "from-green-500 to-green-600",
  },
  {
    title: "Reports & Analytics",
    description: "View branch insights",
    icon: <BarChart3 className="w-6 h-6" />,
    href: "/admin/reports",
    gradient: "from-purple-500 to-purple-600",
  },
];

const recentActivities: RecentActivity[] = [
  {
    id: 1,
    type: "customer",
    message: "New customer registration: Sarah Johnson",
    time: "2 minutes ago",
    status: "success",
  },
  {
    id: 2,
    type: "booking",
    message: "New booking for Advanced Pilates session",
    time: "15 minutes ago",
    status: "info",
  },
  {
    id: 3,
    type: "course",
    message: "Course 'Beginner Yoga' session completed",
    time: "1 hour ago",
    status: "success",
  },
  {
    id: 4,
    type: "payment",
    message: "Payment received: ฿2,500 from John Doe",
    time: "2 hours ago",
    status: "success",
  },
];

export default function BranchManagementDashboard() {
  const { admin } = useAdminAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [stats] = useState<BranchStats>(mockStats);
  const [activities] = useState<RecentActivity[]>(recentActivities);

  useEffect(() => {
    // Redirect if not branch admin
    if (admin && admin.role !== 'branch_admin') {
      router.push('/admin');
      return;
    }

    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, [admin, router]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: 'THB',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'success';
    if (growth < 0) return 'danger';
    return 'default';
  };

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) return <TrendingUp className="w-4 h-4" />;
    if (growth < 0) return <TrendingDown className="w-4 h-4" />;
    return null;
  };

  if (loading) {
    return (
      <AdminLayout title="Branch Management" subtitle="Manage your branch operations">
        <div className="flex justify-center items-center h-64">
          <Spinner size="lg" color="primary" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Branch Management"
      subtitle={`${admin?.branch?.name || 'Branch'} Dashboard`}
    >
      <div className="space-y-8">
        {/* Branch Info */}
        {admin?.branch && (
          <Card className="bg-gradient-to-r from-[#5FCED3]/10 to-[#5FCED3]/5 border-0 shadow-lg">
            <CardBody className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-[#5FCED3]/20 rounded-xl">
                  <MapPin className="w-8 h-8 text-[#5FCED3]" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-slate-800">{admin.branch.name}</h2>
                  <p className="text-slate-600">{admin.branch.address}</p>
                  <Chip size="sm" color="success" variant="flat" className="mt-2">
                    Active Branch
                  </Chip>
                </div>
              </div>
            </CardBody>
          </Card>
        )}

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-white to-blue-50 border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardBody className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 mb-1">
                    Total Customers
                  </p>
                  <p className="text-3xl font-bold text-slate-800">
                    {stats.totalCustomers}
                  </p>
                  <div className="flex items-center mt-2">
                    <Chip
                      size="sm"
                      variant="flat"
                      color="success"
                      className="text-xs"
                    >
                      {stats.activeCustomers} active
                    </Chip>
                  </div>
                </div>
                <div className="p-3 bg-blue-100 rounded-xl">
                  <Users className="w-8 h-8 text-blue-600" />
                </div>
              </div>
            </CardBody>
          </Card>

          <Card className="bg-gradient-to-br from-white to-[#5FCED3]/10 border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardBody className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 mb-1">
                    Total Courses
                  </p>
                  <p className="text-3xl font-bold text-slate-800">
                    {stats.totalCourses}
                  </p>
                  <div className="flex items-center mt-2">
                    <Chip
                      size="sm"
                      variant="flat"
                      color="success"
                      className="text-xs"
                    >
                      {stats.activeCourses} active
                    </Chip>
                  </div>
                </div>
                <div className="p-3 bg-[#5FCED3]/20 rounded-xl">
                  <GraduationCap className="w-8 h-8 text-[#5FCED3]" />
                </div>
              </div>
            </CardBody>
          </Card>

          <Card className="bg-gradient-to-br from-white to-green-50 border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardBody className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 mb-1">
                    Monthly Revenue
                  </p>
                  <p className="text-3xl font-bold text-slate-800">
                    {formatCurrency(stats.monthlyRevenue)}
                  </p>
                  <div className="flex items-center mt-2">
                    {getGrowthIcon(stats.revenueGrowth)}
                    <Chip
                      size="sm"
                      variant="flat"
                      color={getGrowthColor(stats.revenueGrowth)}
                      className="text-xs ml-1"
                    >
                      {stats.revenueGrowth}% vs last month
                    </Chip>
                  </div>
                </div>
                <div className="p-3 bg-green-100 rounded-xl">
                  <DollarSign className="w-8 h-8 text-green-600" />
                </div>
              </div>
            </CardBody>
          </Card>

          <Card className="bg-gradient-to-br from-white to-orange-50 border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardBody className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 mb-1">
                    Today&apos;s Sessions
                  </p>
                  <p className="text-3xl font-bold text-slate-800">
                    {stats.todaySessions}
                  </p>
                  <div className="flex items-center mt-2">
                    <Activity className="w-4 h-4 text-orange-500 mr-1" />
                    <span className="text-xs text-slate-600">
                      {stats.activeBookings} total bookings
                    </span>
                  </div>
                </div>
                <div className="p-3 bg-orange-100 rounded-xl">
                  <Calendar className="w-8 h-8 text-orange-600" />
                </div>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickActions.map((action, index) => (
            <Link key={index} href={action.href}>
              <Card className="group bg-white border-0 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 cursor-pointer overflow-hidden">
                <div className={`h-2 bg-gradient-to-r ${action.gradient}`}></div>
                <CardBody className="p-6">
                  <div className="flex items-center space-x-4 mb-4">
                    <div className={`p-3 rounded-xl bg-gradient-to-r ${action.gradient} text-white group-hover:scale-110 transition-transform duration-300`}>
                      {action.icon}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-slate-800 group-hover:text-slate-900">
                        {action.title}
                      </h3>
                      <p className="text-sm text-slate-600">
                        {action.description}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center text-slate-500 group-hover:text-slate-700 transition-colors">
                    <span className="text-sm font-medium">Manage</span>
                    <ArrowUpRight className="w-4 h-4 ml-1 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                  </div>
                </CardBody>
              </Card>
            </Link>
          ))}
        </div>

        {/* Recent Activities */}
        <Card className="bg-white border-0 shadow-lg">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-semibold text-slate-800">
                  Recent Activities
                </h3>
                <p className="text-sm text-slate-600">Latest updates from your branch</p>
              </div>
              <Button
                variant="bordered"
                size="sm"
                startContent={<Eye className="w-4 h-4" />}
              >
                View All
              </Button>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-4">
              {activities.map((activity) => (
                <div
                  key={activity.id}
                  className="flex items-center space-x-4 p-4 rounded-lg bg-slate-50 hover:bg-slate-100 transition-colors"
                >
                  <div className={`w-3 h-3 rounded-full ${activity.status === 'success' ? 'bg-green-500' :
                    activity.status === 'warning' ? 'bg-yellow-500' :
                      activity.status === 'info' ? 'bg-blue-500' :
                        activity.status === 'danger' ? 'bg-red-500' : 'bg-gray-500'
                    }`}></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-slate-800">
                      {activity.message}
                    </p>
                    <p className="text-xs text-slate-500">{activity.time}</p>
                  </div>
                  <Chip
                    size="sm"
                    variant="flat"
                    color={
                      activity.type === 'customer' ? 'primary' :
                        activity.type === 'booking' ? 'secondary' :
                          activity.type === 'course' ? 'success' :
                            activity.type === 'payment' ? 'warning' : 'default'
                    }
                    className="capitalize"
                  >
                    {activity.type}
                  </Chip>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      </div>
    </AdminLayout>
  );
}
