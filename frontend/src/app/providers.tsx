'use client'

import { Hero<PERSON><PERSON>rovider } from '@heroui/react'
import { ThemeProvider as NextThemesProvider } from 'next-themes'
import { NextIntlClientProvider } from 'next-intl'
import { useEffect, useState } from 'react'
import { AuthProvider } from '@/contexts/AuthContext'
import { LineAuthProvider } from '@/contexts/LineAuthContext'
import { Toaster } from 'react-hot-toast'

export function Providers({ children }: { children: React.ReactNode }) {
  const [messages, setMessages] = useState<Record<string, unknown> | null>(null);
  const [locale, setLocale] = useState('th');

  useEffect(() => {
    // Get locale from cookie
    const cookieLocale = document.cookie
      .split('; ')
      .find(row => row.startsWith('locale='))
      ?.split('=')[1] || 'th';

    setLocale(cookieLocale);

    // Load messages
    import(`../../messages/${cookieLocale}.json`)
      .then(module => setMessages(module.default))
      .catch(() => import(`../../messages/th.json`).then(module => setMessages(module.default)));
  }, []);

  if (!messages) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex items-center justify-center">
        <div className="text-center space-y-4">
          {/* Elegant Spinner */}
          <div className="relative">
            <div className="w-12 h-12 border-4 border-gray-100 rounded-full animate-spin">
              <div className="absolute inset-0 border-4 border-transparent border-t-[#5FCED3] rounded-full animate-spin"></div>
            </div>
            {/* Pulsing center dot */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-2 h-2 bg-[#5FCED3] rounded-full animate-pulse"></div>
            </div>
          </div>

          {/* Loading message */}
          <div className="space-y-2">
            <p className="text-slate-700 font-medium text-lg">Loading BodyLabs...</p>
            <div className="flex justify-center space-x-1">
              <div className="w-2 h-2 bg-[#5FCED3] rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 bg-[#5FCED3] rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 bg-[#5FCED3] rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <NextIntlClientProvider locale={locale} messages={messages}>
      <AuthProvider>
        <LineAuthProvider>
          <HeroUIProvider>
            <NextThemesProvider attribute="class" defaultTheme="light">
              {children}
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: '#363636',
                    color: '#fff',
                  },
                }}
              />
            </NextThemesProvider>
          </HeroUIProvider>
        </LineAuthProvider>
      </AuthProvider>
    </NextIntlClientProvider>
  )
}
