'use client'

import { <PERSON>, CardBody, CardHeader } from "@heroui/react";
import { useTranslations } from 'next-intl';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import LineAuth from '@/components/LineAuth';
import BookingInterface from '@/components/BookingInterface';
import AdminDashboard from '@/components/AdminDashboard';
import { useAuth } from '@/contexts/AuthContext';
import { useLineAuth } from '@/contexts/LineAuthContext';

export default function Home() {
  const t = useTranslations('home');
  const { user, loading: authLoading } = useAuth();
  const { loading: lineLoading } = useLineAuth();

  if (authLoading || lineLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex items-center justify-center">
        <div className="text-center space-y-4">
          {/* Elegant Spinner */}
          <div className="relative">
            <div className="w-16 h-16 border-4 border-gray-100 rounded-full animate-spin">
              <div className="absolute inset-0 border-4 border-transparent border-t-[#5FCED3] rounded-full animate-spin"></div>
            </div>
            {/* Pulsing center dot */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-2 h-2 bg-[#5FCED3] rounded-full animate-pulse"></div>
            </div>
          </div>

          {/* Loading message */}
          <div className="space-y-2">
            <p className="text-slate-700 font-medium text-xl">กำลังโหลด...</p>
            <div className="flex justify-center space-x-1">
              <div className="w-2 h-2 bg-[#5FCED3] rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 bg-[#5FCED3] rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 bg-[#5FCED3] rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show appropriate interface based on user role
  if (user) {
    if (user.role === 'branch_admin' || user.role === 'super_admin') {
      return (
        <div className="min-h-screen bg-gray-50">
          <div className="absolute top-4 right-4">
            <LanguageSwitcher />
          </div>
          <AdminDashboard />
        </div>
      );
    } else {
      return (
        <div className="min-h-screen bg-gray-50">
          <div className="absolute top-4 right-4">
            <LanguageSwitcher />
          </div>
          <BookingInterface />
        </div>
      );
    }
  }

  // Show login interface if not authenticated
  return (
    <div className="min-h-screen p-8 flex flex-col items-center justify-center gap-8">
      <div className="absolute top-4 right-4">
        <LanguageSwitcher />
      </div>

      <Card className="max-w-md">
        <CardHeader className="pb-0 pt-2 px-4 flex-col items-start">
          <h1 className="text-2xl font-bold text-primary">{t('title')}</h1>
          <p className="text-small text-default-500">{t('subtitle')}</p>
        </CardHeader>
        <CardBody className="overflow-visible py-2">
          <p className="text-default-600 mb-4">
            {t('description')}
          </p>
          <p className="text-sm text-gray-600 mb-4">
            กรุณาเข้าสู่ระบบผ่าน LINE เพื่อใช้งานระบบจองคลาส Pilates
          </p>
        </CardBody>
      </Card>

      {/* LINE Authentication */}
      <LineAuth />

      <div className="text-center">
        <p className="text-small text-default-500">
          {t('hero_message')}
        </p>
        <p className="text-xs text-gray-500 mt-2">
          ระบบจองคลาส Pilates BodyLabs - เข้าสู่ระบบด้วย LINE OA
        </p>
      </div>
    </div>
  );
}
