import type { Metada<PERSON> } from "next";
import "./globals.css";
import { Providers } from "./providers";
import { notoSansThai } from '@/lib/fonts';



export const metadata: Metadata = {
  title: "BodyLab Pilates Reservation",
  description: "Pilates class reservation system for BodyLab",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="th">
      <body
        className={`${notoSansThai.variable} ${notoSansThai.className} antialiased`}
      >
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
