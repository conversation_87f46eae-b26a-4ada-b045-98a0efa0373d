'use client'

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, CardBody, CardHeader, Avatar, Chip, Progress } from "@heroui/react";
import { useTranslations } from 'next-intl';
import { useLineAuth } from '@/contexts/LineAuthContext';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import LiffTester from '@/components/LiffTester';

interface CustomerCourse {
  id: string;
  name: string;
  description?: string;
  totalSessions: number;
  usedSessions: number;
  remainingSessions: number;
  expiryDate: string;
  status: 'active' | 'expired' | 'completed';
}

export default function CustomerHome() {
  const t = useTranslations('home');
  const tCommon = useTranslations('common');
  const { profile } = useLineAuth();
  const [courses, setCourses] = useState<CustomerCourse[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (profile) {
      loadCourses();
    }
  }, [profile]);

  const loadCourses = async () => {
    try {
      setLoading(true);
      // Mock data for now - will be replaced with actual API call
      setCourses([
        {
          id: '1',
          name: 'Pilates Foundation Course',
          description: 'Basic pilates course for beginners',
          totalSessions: 10,
          usedSessions: 3,
          remainingSessions: 7,
          expiryDate: '2025-12-31',
          status: 'active'
        },
        {
          id: '2',
          name: 'Advanced Pilates',
          description: 'Advanced techniques and poses',
          totalSessions: 8,
          usedSessions: 8,
          remainingSessions: 0,
          expiryDate: '2025-06-30',
          status: 'completed'
        }
      ]);
    } catch (error) {
      console.error('Error loading courses:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'expired': return 'warning';
      case 'completed': return 'default';
      default: return 'default';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('th-TH');
  };

  const isExpiringSoon = (expiryDate: string) => {
    const expiry = new Date(expiryDate);
    const now = new Date();
    const daysUntilExpiry = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= 7 && daysUntilExpiry > 0;
  };

  return (
    <div className="p-4 space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-primary">{t('title')}</h1>
          <p className="text-small text-default-500">{t('subtitle')}</p>
        </div>
        <LanguageSwitcher />
      </div>

      {/* User Profile Card */}
      {profile && (
        <Card>
          <CardBody>
            <div className="flex items-center gap-3">
              <Avatar
                src={profile.pictureUrl}
                name={profile.displayName}
                size="md"
              />
              <div className="flex-1">
                <p className="font-medium">{profile.displayName}</p>
                <p className="text-small text-default-500">
                  {tCommon('welcome')}!
                </p>
              </div>
              {/* Account Status Indicator */}
              <div className="text-right">
                <Chip
                  color="warning"
                  variant="flat"
                  size="sm"
                >
                  Pending Activation
                </Chip>
                <p className="text-xs text-default-400 mt-1">
                  Contact admin to activate
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Account Status Notice for Inactive Users */}
      <Card className="border-warning-200 bg-warning-50">
        <CardBody>
          <div className="flex items-start gap-3">
            <div className="text-warning text-xl">⚠️</div>
            <div className="flex-1">
              <h3 className="font-semibold text-warning-800 mb-2">Account Activation Required</h3>
              <p className="text-warning-700 text-sm mb-3">
                Your account is currently inactive. To start booking sessions and accessing all features,
                please contact our admin team or visit our studio to activate your account.
              </p>
              <div className="text-sm text-warning-600">
                <p><strong>What you can do:</strong></p>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Browse available courses and schedules</li>
                  <li>View studio information</li>
                  <li>Contact us for account activation</li>
                </ul>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* My Courses */}
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold">My Courses</h2>
        </CardHeader>
        <CardBody className="space-y-4">
          {loading ? (
            <div className="text-center py-4">
              <p className="text-default-500">Loading courses...</p>
            </div>
          ) : courses.length === 0 ? (
            <div className="text-center py-4">
              <p className="text-default-500">No courses found</p>
            </div>
          ) : (
            courses.map((course) => (
              <Card key={course.id} className="border">
                <CardBody>
                  <div className="space-y-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold">{course.name}</h3>
                        {course.description && (
                          <p className="text-sm text-default-500">{course.description}</p>
                        )}
                      </div>
                      <Chip
                        color={getStatusColor(course.status)}
                        variant="flat"
                        size="sm"
                      >
                        {course.status}
                      </Chip>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Sessions Used</span>
                        <span>{course.usedSessions}/{course.totalSessions}</span>
                      </div>
                      <Progress
                        value={(course.usedSessions / course.totalSessions) * 100}
                        color={course.remainingSessions > 0 ? "primary" : "default"}
                        size="sm"
                      />
                      <div className="flex justify-between text-sm">
                        <span>Remaining Sessions</span>
                        <span className={course.remainingSessions === 0 ? 'text-default-400' : 'text-success'}>
                          {course.remainingSessions}
                        </span>
                      </div>
                    </div>

                    <div className="flex justify-between items-center pt-2 border-t">
                      <div>
                        <p className="text-sm text-default-500">Expires</p>
                        <p className={`text-sm font-medium ${isExpiringSoon(course.expiryDate) ? 'text-warning' : ''}`}>
                          {formatDate(course.expiryDate)}
                          {isExpiringSoon(course.expiryDate) && (
                            <span className="ml-2 text-warning">⚠️ Expiring Soon</span>
                          )}
                        </p>
                      </div>
                      {course.remainingSessions > 0 && course.status === 'active' && (
                        <Button
                          color="primary"
                          size="sm"
                          variant="flat"
                        >
                          Book Session
                        </Button>
                      )}
                    </div>
                  </div>
                </CardBody>
              </Card>
            ))
          )}
        </CardBody>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-2 gap-4">
        <Button
          color="primary"
          variant="solid"
          className="h-20 flex-col"
          as="a"
          href="/customer/book"
        >
          <span className="text-lg">📅</span>
          <span>{tCommon('book_class')}</span>
        </Button>

        <Button
          color="primary"
          variant="bordered"
          className="h-20 flex-col"
          as="a"
          href="/customer/schedule"
        >
          <span className="text-lg">📋</span>
          <span>{tCommon('view_schedule')}</span>
        </Button>
      </div>

      {/* My Bookings */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">My Bookings</h3>
        </CardHeader>
        <CardBody>
          <p className="text-default-600 text-center py-4">
            No upcoming bookings
          </p>
          <Button
            color="primary"
            variant="light"
            className="w-full"
            as="a"
            href="/customer/bookings"
          >
            View All Bookings
          </Button>
        </CardBody>
      </Card>

      {/* LIFF Testing (Development Only) */}
      <LiffTester />

      {/* Footer */}
      <div className="text-center py-4">
        <p className="text-small text-default-500">
          Customer Mobile Interface
        </p>
      </div>
    </div>
  );
}
