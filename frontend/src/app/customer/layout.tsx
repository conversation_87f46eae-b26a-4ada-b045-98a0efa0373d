'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useLineAuth } from '@/contexts/LineAuthContext'
import { Spinner } from '@heroui/react'

export default function CustomerLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { isLiffReady, isLoggedIn, loading } = useLineAuth()
  const router = useRouter()

  useEffect(() => {
    // Redirect to admin if not in LIFF environment
    if (!loading && !isLiffReady) {
      router.push('/admin')
    }
  }, [loading, isLiffReady, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spinner size="lg" color="primary" />
      </div>
    )
  }

  if (!isLiffReady) {
    return null // Will redirect to admin
  }

  if (!isLoggedIn) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4"><PERSON><PERSON><PERSON> Required</h1>
          <p className="text-default-600">
            Please log in with your LINE account to access the reservation system.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile-optimized layout */}
      <div className="max-w-md mx-auto">
        {children}
      </div>
    </div>
  )
}
