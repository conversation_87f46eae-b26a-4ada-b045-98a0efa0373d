'use client'

import { <PERSON><PERSON>, <PERSON>, CardBody, CardHeader } from "@heroui/react";
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';

export default function BookClass() {
  const t = useTranslations('common');
  const router = useRouter();

  return (
    <div className="p-4 space-y-4">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          isIconOnly
          variant="light"
          onClick={() => router.back()}
        >
          ←
        </Button>
        <h1 className="text-xl font-bold">{t('book_class')}</h1>
      </div>

      {/* Coming Soon */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Book a Class</h3>
        </CardHeader>
        <CardBody className="text-center py-8">
          <p className="text-default-600 mb-4">
            Class booking feature is coming soon!
          </p>
          <p className="text-small text-default-500">
            You&apos;ll be able to browse available classes and book your sessions here.
          </p>
        </CardBody>
      </Card>
    </div>
  );
}
