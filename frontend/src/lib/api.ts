const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001/api";

export interface User {
  id: number;
  lineUserId: string;
  displayName: string;
  email?: string;
  phoneNumber?: string;
  profilePictureUrl?: string;
  role: "super_admin" | "branch_admin" | "trainer" | "customer";
  membershipLevel: "bronze" | "silver" | "gold" | "platinum";
  totalSpent: number;
  hasGivenConsent: boolean;
  isActive: boolean;
  createdAt: string;
  lastLoginAt?: string;
  branch?: {
    id: number;
    name: string;
    address: string;
  };
}

export interface Admin {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  phoneNumber?: string;
  role: "super_admin" | "branch_admin";
  isActive: boolean;
  lastLoginAt?: string;
  branch?: {
    id: number;
    name: string;
    address: string;
  };
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateAdminDto {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  role: "super_admin" | "branch_admin";
  branchId?: number;
}

export interface UpdateAdminDto {
  email?: string;
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  role?: "super_admin" | "branch_admin";
  branchId?: number;
}

export interface LoginResponse {
  success: boolean;
  data: {
    user: User;
    token: string;
  };
}

export interface AdminLoginResponse {
  success: boolean;
  data: {
    admin: Admin;
    token: string;
  };
}

export interface Branch {
  id: number;
  name: string;
  address: string;
  phoneNumber?: string;
  email?: string;
  openTime?: string;
  closeTime?: string;
  operatingDays?: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  users?: User[];
  courses?: Course[];
  bookings?: Booking[];
}

export interface CreateBranchRequest {
  name: string;
  address: string;
  phoneNumber?: string;
  email?: string;
  openTime: string;
  closeTime: string;
  operatingDays: string[];
  isActive?: boolean;
}

export interface UpdateBranchRequest {
  name?: string;
  address?: string;
  phoneNumber?: string;
  email?: string;
  openTime?: string;
  closeTime?: string;
  operatingDays?: string[];
  isActive?: boolean;
}

export interface BranchQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  isActive?: boolean;
  sortBy?: string;
  sortOrder?: "ASC" | "DESC";
}

export interface BranchListResponse {
  branches: Branch[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface BranchStatsParams {
  branchId?: number;
  period?: "day" | "week" | "month" | "year";
  startDate?: string;
  endDate?: string;
}

export interface BranchStats {
  totalUsers: number;
  activeUsers: number;
  totalCourses: number;
  activeCourses: number;
  totalBookings: number;
  completedBookings: number;
  totalRevenue: number;
  period: string;
  branchId?: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

export interface Booking {
  id: string;
  scheduledDateTime: string;
  durationMinutes: number;
  status: "scheduled" | "confirmed" | "completed" | "cancelled" | "no_show";
  notes?: string;
  cancellationReason?: string;
  frontConfirmed: boolean;
  trainerConfirmed: boolean;
  frontConfirmedAt?: string;
  trainerConfirmedAt?: string;
  sessionNotes?: string;
  bodyMetrics?: Record<string, unknown>;
  trainerRecommendations?: string;
  customer: User;
  trainer: User;
  course: Course;
  branch: {
    id: number;
    name: string;
    address: string;
  };
}

export interface Course {
  id: number;
  name: string;
  description?: string;
  price: number;
  totalSessions: number;
  usedSessions: number;
  remainingSessions: number;
  startDate: string;
  expiryDate: string;
  status: "active" | "expired" | "suspended" | "transferred";
  notes?: string;
  customer: User;
  branch: {
    id: number;
    name: string;
    address: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CourseSession {
  id: number;
  courseId: number;
  bookingId?: number;
  sessionDate?: string;
  status: "available" | "booked" | "completed" | "cancelled";
  notes?: string;
  createdAt: string;
}

export interface TransferSessionsResponse {
  success: boolean;
  message: string;
  fromCourse: Course;
  toCourse: Course;
}

export interface DeleteResponse {
  success: boolean;
  message: string;
}

export interface UserBranchAccess {
  id: number;
  user: User;
  branch: {
    id: number;
    name: string;
    address: string;
  };
  accessLevel: "reservation" | "full_access";
  isActive: boolean;
  notes?: string;
  grantedAt?: string;
  revokedAt?: string;
  grantedBy?: User;
  revokedBy?: User;
  createdAt: string;
  updatedAt: string;
}

export interface GrantBranchAccessRequest {
  userId: number;
  branchId: number;
  accessLevel: "reservation" | "full_access";
  notes?: string;
}

export interface GrantMultipleBranchAccessRequest {
  userId: number;
  branchAccess: {
    branchId: number;
    accessLevel: "reservation" | "full_access";
    notes?: string;
  }[];
}

export interface UpdateBranchAccessRequest {
  accessLevel?: "reservation" | "full_access";
  isActive?: boolean;
  notes?: string;
}

export interface RevokeBranchAccessRequest {
  userId: number;
  branchId: number;
  reason?: string;
}

export interface BulkRevokeBranchAccessRequest {
  userId: number;
  branchIds: number[];
  reason?: string;
}

export interface BranchAccessQueryParams {
  userId?: number;
  branchId?: number;
  accessLevel?: "reservation" | "full_access";
  isActive?: boolean;
  page?: number;
  limit?: number;
}

export interface BulkBranchAccessResult {
  success: UserBranchAccess[];
  errors: {
    branchId: number;
    error: string;
  }[];
  totalGranted: number;
  totalErrors: number;
}

export interface BulkRevokeBranchAccessResult {
  success: UserBranchAccess[];
  errors: {
    branchId: number;
    error: string;
  }[];
  totalRevoked: number;
  totalErrors: number;
}

class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor() {
    this.baseURL = API_BASE_URL;
    // Try to get token from localStorage on client side
    if (typeof window !== "undefined") {
      this.token = localStorage.getItem("auth_token");
    }
  }

  setToken(token: string | null) {
    this.token = token;
    if (typeof window !== "undefined") {
      if (token) {
        localStorage.setItem("auth_token", token);
      } else {
        localStorage.removeItem("auth_token");
      }
    }
  }

  getToken(): string | null {
    if (typeof window !== "undefined") {
      return localStorage.getItem("auth_token");
    }
    return this.token;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Add any additional headers from options
    if (options.headers) {
      Object.assign(headers, options.headers);
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `HTTP error! status: ${response.status}`
      );
    }

    return response.json();
  }

  // Auth endpoints
  async loginWithLine(
    accessToken: string,
    email?: string | null
  ): Promise<LoginResponse> {
    const body: { accessToken: string; email?: string } = { accessToken };
    if (email) {
      body.email = email;
    }

    return this.request<LoginResponse>("/auth/line/login", {
      method: "POST",
      body: JSON.stringify(body),
    });
  }

  async getProfile(): Promise<ApiResponse<User>> {
    return this.request<ApiResponse<User>>("/auth/profile");
  }

  async logout(): Promise<ApiResponse<{ message: string }>> {
    return this.request<ApiResponse<{ message: string }>>("/auth/logout", {
      method: "POST",
    });
  }

  // Admin auth endpoints
  async adminLogin(
    email: string,
    password: string,
    branchId?: string
  ): Promise<AdminLoginResponse> {
    const body: { email: string; password: string; branchId?: string } = {
      email,
      password,
    };
    if (branchId) {
      body.branchId = branchId;
    }

    return this.request<AdminLoginResponse>("/admin/auth/login", {
      method: "POST",
      body: JSON.stringify(body),
    });
  }

  async getAdminProfile(): Promise<ApiResponse<Admin>> {
    return this.request<ApiResponse<Admin>>("/admin/auth/profile");
  }

  async adminLogout(): Promise<ApiResponse<{ message: string }>> {
    return this.request<ApiResponse<{ message: string }>>(
      "/admin/auth/logout",
      {
        method: "POST",
      }
    );
  }

  // Booking endpoints
  async getBookings(filters?: {
    status?: string;
    trainerId?: string;
    customerId?: string;
    branchId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<ApiResponse<Booking[]>> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });
    }

    const endpoint = `/bookings${
      params.toString() ? `?${params.toString()}` : ""
    }`;
    return this.request<ApiResponse<Booking[]>>(endpoint);
  }

  async getBookingById(id: string): Promise<ApiResponse<Booking>> {
    return this.request<ApiResponse<Booking>>(`/bookings/${id}`);
  }

  async createBooking(data: {
    courseId: number;
    trainerId: number;
    scheduledDateTime: string;
    durationMinutes?: number;
    notes?: string;
  }): Promise<ApiResponse<Booking>> {
    return this.request<ApiResponse<Booking>>("/bookings", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async cancelBooking(
    id: string,
    cancellationReason?: string
  ): Promise<ApiResponse<Booking>> {
    return this.request<ApiResponse<Booking>>(`/bookings/${id}/cancel`, {
      method: "PUT",
      body: JSON.stringify({ cancellationReason }),
    });
  }

  async confirmSessionFront(id: string): Promise<ApiResponse<Booking>> {
    return this.request<ApiResponse<Booking>>(`/bookings/${id}/confirm/front`, {
      method: "PUT",
    });
  }

  async confirmSessionTrainer(
    id: string,
    data: {
      sessionNotes?: string;
      bodyMetrics?: Record<string, unknown>;
      trainerRecommendations?: string;
    }
  ): Promise<ApiResponse<Booking>> {
    return this.request<ApiResponse<Booking>>(
      `/bookings/${id}/confirm/trainer`,
      {
        method: "PUT",
        body: JSON.stringify(data),
      }
    );
  }

  // Course endpoints
  async getCourses(filters?: {
    customerId?: number;
    branchId?: number;
    status?: string;
  }): Promise<ApiResponse<Course[]>> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value.toString());
      });
    }

    const endpoint = `/courses${
      params.toString() ? `?${params.toString()}` : ""
    }`;
    return this.request<ApiResponse<Course[]>>(endpoint);
  }

  async getCourseById(id: number): Promise<ApiResponse<Course>> {
    return this.request<ApiResponse<Course>>(`/courses/${id}`);
  }

  async createCourse(data: {
    name: string;
    description?: string;
    price: number;
    totalSessions: number;
    startDate: string;
    expiryDate: string;
    customerId: number;
    branchId: number;
    notes?: string;
  }): Promise<ApiResponse<Course>> {
    return this.request<ApiResponse<Course>>("/courses", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateCourse(
    id: number,
    data: {
      name?: string;
      description?: string;
      totalSessions?: number;
      expiryDate?: string;
      status?: string;
      notes?: string;
    }
  ): Promise<ApiResponse<Course>> {
    return this.request<ApiResponse<Course>>(`/courses/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async transferSessions(data: {
    fromCourseId: number;
    toCourseId: number;
    sessionsToTransfer: number;
  }): Promise<ApiResponse<{ fromCourse: Course; toCourse: Course }>> {
    return this.request<ApiResponse<{ fromCourse: Course; toCourse: Course }>>(
      "/courses/transfer-sessions",
      {
        method: "POST",
        body: JSON.stringify(data),
      }
    );
  }

  // Admin user management endpoints
  async getUsers(): Promise<
    ApiResponse<{
      users: User[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>
  > {
    return this.request<
      ApiResponse<{
        users: User[];
        pagination: {
          page: number;
          limit: number;
          total: number;
          totalPages: number;
        };
      }>
    >("/admin/users");
  }

  async getAdminUsers(params?: string): Promise<
    ApiResponse<{
      users: User[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>
  > {
    const endpoint = params ? `/admin/users?${params}` : "/admin/users";
    return this.request<
      ApiResponse<{
        users: User[];
        pagination: {
          page: number;
          limit: number;
          total: number;
          totalPages: number;
        };
      }>
    >(endpoint);
  }

  async getBranches(
    params?: BranchQueryParams
  ): Promise<ApiResponse<BranchListResponse>> {
    const queryString = params
      ? `?${new URLSearchParams(
          Object.entries(params).reduce((acc, [key, value]) => {
            if (value !== undefined && value !== null) {
              acc[key] = String(value);
            }
            return acc;
          }, {} as Record<string, string>)
        ).toString()}`
      : "";
    return this.request<ApiResponse<BranchListResponse>>(
      `/admin/branches${queryString}`
    );
  }

  async getBranchById(id: number): Promise<ApiResponse<Branch>> {
    return this.request<ApiResponse<Branch>>(`/admin/branches/${id}`);
  }

  async createBranch(data: CreateBranchRequest): Promise<ApiResponse<Branch>> {
    return this.request<ApiResponse<Branch>>("/admin/branches", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateBranch(
    id: number,
    data: UpdateBranchRequest
  ): Promise<ApiResponse<Branch>> {
    return this.request<ApiResponse<Branch>>(`/admin/branches/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async deleteBranch(id: number): Promise<ApiResponse<void>> {
    return this.request<ApiResponse<void>>(`/admin/branches/${id}`, {
      method: "DELETE",
    });
  }

  async toggleBranchStatus(id: number): Promise<ApiResponse<Branch>> {
    return this.request<ApiResponse<Branch>>(
      `/admin/branches/${id}/toggle-status`,
      {
        method: "PUT",
      }
    );
  }

  async getBranchStats(
    params?: BranchStatsParams
  ): Promise<ApiResponse<BranchStats>> {
    const queryString = params
      ? `?${new URLSearchParams(
          Object.entries(params).reduce((acc, [key, value]) => {
            if (value !== undefined && value !== null) {
              acc[key] = String(value);
            }
            return acc;
          }, {} as Record<string, string>)
        ).toString()}`
      : "";
    return this.request<ApiResponse<BranchStats>>(
      `/admin/branches/stats${queryString}`
    );
  }

  async activateUser(
    userId: number,
    reason?: string
  ): Promise<ApiResponse<User>> {
    return this.request<ApiResponse<User>>(`/admin/users/${userId}/activate`, {
      method: "PUT",
      body: JSON.stringify({ reason }),
    });
  }

  async deactivateUser(
    userId: number,
    reason?: string
  ): Promise<ApiResponse<User>> {
    return this.request<ApiResponse<User>>(
      `/admin/users/${userId}/deactivate`,
      {
        method: "PUT",
        body: JSON.stringify({ reason }),
      }
    );
  }

  // Admin Course Management endpoints
  async getAdminCourses(filters?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    customerId?: number;
  }): Promise<
    ApiResponse<{
      courses: Course[];
      total: number;
      page: number;
      limit: number;
    }>
  > {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
    }

    const endpoint = `/admin/courses${
      params.toString() ? `?${params.toString()}` : ""
    }`;
    return this.request<
      ApiResponse<{
        courses: Course[];
        total: number;
        page: number;
        limit: number;
      }>
    >(endpoint);
  }

  async getAdminCourseById(id: number): Promise<ApiResponse<Course>> {
    return this.request<ApiResponse<Course>>(`/admin/courses/${id}`);
  }

  async getCourseSessions(id: number): Promise<ApiResponse<CourseSession[]>> {
    return this.request<ApiResponse<CourseSession[]>>(
      `/admin/courses/${id}/sessions`
    );
  }

  async createAdminCourse(data: {
    name: string;
    description?: string;
    price: number;
    totalSessions: number;
    startDate: string;
    expiryDate: string;
    customerId: number;
    branchId?: number;
  }): Promise<ApiResponse<Course>> {
    return this.request<ApiResponse<Course>>("/admin/courses", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateAdminCourse(
    id: number,
    data: {
      name?: string;
      description?: string;
      price?: number;
      startDate?: string;
      expiryDate?: string;
      status?: string;
    }
  ): Promise<ApiResponse<Course>> {
    return this.request<ApiResponse<Course>>(`/admin/courses/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async transferCourseSessions(
    fromCourseId: number,
    data: {
      toCourseId: number;
      sessionCount: number;
      reason: string;
    }
  ): Promise<ApiResponse<TransferSessionsResponse>> {
    return this.request<ApiResponse<TransferSessionsResponse>>(
      `/admin/courses/${fromCourseId}/transfer-sessions`,
      {
        method: "POST",
        body: JSON.stringify(data),
      }
    );
  }

  async extendCourseExpiry(
    id: number,
    newExpiryDate: string
  ): Promise<ApiResponse<Course>> {
    return this.request<ApiResponse<Course>>(
      `/admin/courses/${id}/extend-expiry`,
      {
        method: "PUT",
        body: JSON.stringify({ newExpiryDate }),
      }
    );
  }

  async addCourseSessions(
    id: number,
    additionalSessions: number,
    reason: string
  ): Promise<ApiResponse<Course>> {
    return this.request<ApiResponse<Course>>(
      `/admin/courses/${id}/add-sessions`,
      {
        method: "PUT",
        body: JSON.stringify({ additionalSessions, reason }),
      }
    );
  }

  async deleteCourse(id: number): Promise<ApiResponse<DeleteResponse>> {
    return this.request<ApiResponse<DeleteResponse>>(`/admin/courses/${id}`, {
      method: "DELETE",
    });
  }

  // Branch Access Management
  async grantBranchAccess(
    data: GrantBranchAccessRequest
  ): Promise<ApiResponse<UserBranchAccess>> {
    return this.request<ApiResponse<UserBranchAccess>>(
      "/admin/user-branch-access/grant",
      {
        method: "POST",
        body: JSON.stringify(data),
      }
    );
  }

  async grantMultipleBranchAccess(
    data: GrantMultipleBranchAccessRequest
  ): Promise<ApiResponse<BulkBranchAccessResult>> {
    return this.request<ApiResponse<BulkBranchAccessResult>>(
      "/admin/user-branch-access/grant-multiple",
      {
        method: "POST",
        body: JSON.stringify(data),
      }
    );
  }

  async updateBranchAccess(
    accessId: number,
    data: UpdateBranchAccessRequest
  ): Promise<ApiResponse<UserBranchAccess>> {
    return this.request<ApiResponse<UserBranchAccess>>(
      `/admin/user-branch-access/${accessId}`,
      {
        method: "PUT",
        body: JSON.stringify(data),
      }
    );
  }

  async revokeBranchAccess(
    data: RevokeBranchAccessRequest
  ): Promise<ApiResponse<UserBranchAccess>> {
    return this.request<ApiResponse<UserBranchAccess>>(
      "/admin/user-branch-access/revoke",
      {
        method: "POST",
        body: JSON.stringify(data),
      }
    );
  }

  async bulkRevokeBranchAccess(
    data: BulkRevokeBranchAccessRequest
  ): Promise<ApiResponse<BulkRevokeBranchAccessResult>> {
    return this.request<ApiResponse<BulkRevokeBranchAccessResult>>(
      "/admin/user-branch-access/bulk-revoke",
      {
        method: "POST",
        body: JSON.stringify(data),
      }
    );
  }

  async getUserBranchAccess(
    userId: number
  ): Promise<ApiResponse<UserBranchAccess[]>> {
    return this.request<ApiResponse<UserBranchAccess[]>>(
      `/admin/user-branch-access/user/${userId}`,
      {
        method: "GET",
      }
    );
  }

  async getBranchUserAccess(
    branchId: number
  ): Promise<ApiResponse<UserBranchAccess[]>> {
    return this.request<ApiResponse<UserBranchAccess[]>>(
      `/admin/user-branch-access/branch/${branchId}`,
      {
        method: "GET",
      }
    );
  }

  async getAllBranchAccess(params?: BranchAccessQueryParams): Promise<
    ApiResponse<{
      access: UserBranchAccess[];
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    }>
  > {
    const queryParams = new URLSearchParams();
    if (params?.userId) queryParams.append("userId", params.userId.toString());
    if (params?.branchId)
      queryParams.append("branchId", params.branchId.toString());
    if (params?.accessLevel)
      queryParams.append("accessLevel", params.accessLevel);
    if (params?.isActive !== undefined)
      queryParams.append("isActive", params.isActive.toString());

    // Always provide default values for page and limit
    const page = params?.page && params.page > 0 ? params.page : 1;
    const limit = params?.limit && params.limit > 0 ? params.limit : 10;
    queryParams.append("page", page.toString());
    queryParams.append("limit", limit.toString());

    return this.request<
      ApiResponse<{
        access: UserBranchAccess[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
      }>
    >(`/admin/user-branch-access?${queryParams}`, {
      method: "GET",
    });
  }

  async getCustomerEligibleBranches(
    userId: number
  ): Promise<ApiResponse<Branch[]>> {
    return this.request(
      `/admin/user-branch-access/customer/${userId}/eligible-branches`,
      {
        method: "GET",
      }
    );
  }

  async checkCustomerBranchAccess(
    userId: number,
    branchId: number
  ): Promise<ApiResponse<{ hasAccess: boolean }>> {
    return this.request(
      `/admin/user-branch-access/check/${userId}/${branchId}`,
      {
        method: "GET",
      }
    );
  }

  // Admin Management Methods
  async getAllAdmins(): Promise<ApiResponse<Admin[]>> {
    return this.request<ApiResponse<Admin[]>>("/admin/auth/admins", {
      method: "GET",
    });
  }

  async getAdminById(id: number): Promise<ApiResponse<Admin>> {
    return this.request<ApiResponse<Admin>>(`/admin/auth/admins/${id}`, {
      method: "GET",
    });
  }

  async createAdmin(adminData: CreateAdminDto): Promise<ApiResponse<Admin>> {
    return this.request<ApiResponse<Admin>>("/admin/auth/create", {
      method: "POST",
      body: JSON.stringify(adminData),
    });
  }

  async updateAdmin(
    id: number,
    adminData: UpdateAdminDto
  ): Promise<ApiResponse<Admin>> {
    return this.request<ApiResponse<Admin>>(`/admin/auth/admins/${id}`, {
      method: "PUT",
      body: JSON.stringify(adminData),
    });
  }

  async deactivateAdmin(id: number): Promise<ApiResponse<Admin>> {
    return this.request<ApiResponse<Admin>>(
      `/admin/auth/admins/${id}/deactivate`,
      {
        method: "PUT",
      }
    );
  }
}

export const apiClient = new ApiClient();
