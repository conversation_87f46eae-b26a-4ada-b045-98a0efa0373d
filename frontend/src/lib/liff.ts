"use client";

import liff from "@line/liff";

export interface LiffProfile {
  userId: string;
  displayName: string;
  pictureUrl?: string;
  statusMessage?: string;
}

class LiffService {
  private initialized = false;
  private liffId: string;

  constructor() {
    this.liffId = process.env.NEXT_PUBLIC_LINE_LIFF_ID || "";
  }

  async init(): Promise<boolean> {
    if (this.initialized) return true;

    if (!this.liffId) {
      console.warn("LINE LIFF ID not configured");
      return false;
    }

    try {
      await liff.init({ liffId: this.liffId });
      this.initialized = true;
      return true;
    } catch (error) {
      console.error("LIFF initialization failed:", error);
      return false;
    }
  }

  isLoggedIn(): boolean {
    if (!this.initialized) return false;
    return liff.isLoggedIn();
  }

  async login(): Promise<void> {
    if (!this.initialized) {
      throw new Error("LIFF not initialized");
    }

    if (!this.isLoggedIn()) {
      liff.login();
    }
  }

  async logout(): Promise<void> {
    if (!this.initialized) {
      throw new Error("LIFF not initialized");
    }

    liff.logout();
  }

  async getProfile(): Promise<LiffProfile | null> {
    if (!this.initialized || !this.isLoggedIn()) {
      return null;
    }

    try {
      const profile = await liff.getProfile();
      return {
        userId: profile.userId,
        displayName: profile.displayName,
        pictureUrl: profile.pictureUrl,
        statusMessage: profile.statusMessage,
      };
    } catch (error) {
      console.error("Failed to get profile:", error);
      return null;
    }
  }

  async getAccessToken(): Promise<string | null> {
    if (!this.initialized || !this.isLoggedIn()) {
      return null;
    }

    try {
      return liff.getAccessToken();
    } catch (error) {
      console.error("Failed to get access token:", error);
      return null;
    }
  }

  async getDecodedIDToken(): Promise<{
    email?: string;
    [key: string]: unknown;
  } | null> {
    if (!this.initialized || !this.isLoggedIn()) {
      return null;
    }

    try {
      return liff.getDecodedIDToken() as {
        email?: string;
        [key: string]: unknown;
      } | null;
    } catch (error) {
      console.error("Failed to get decoded ID token:", error);
      return null;
    }
  }

  getOS(): string {
    if (!this.initialized) return "unknown";
    return liff.getOS() || "unknown";
  }

  getVersion(): string {
    if (!this.initialized) return "unknown";
    return liff.getVersion() || "unknown";
  }

  getLanguage(): string {
    if (!this.initialized) return "en";
    return liff.getLanguage() || "en";
  }

  isInClient(): boolean {
    if (!this.initialized) return false;
    return liff.isInClient();
  }

  isApiAvailable(apiName: string): boolean {
    if (!this.initialized) return false;
    return liff.isApiAvailable(apiName);
  }

  async openWindow(url: string, external = false): Promise<void> {
    if (!this.initialized) {
      throw new Error("LIFF not initialized");
    }

    liff.openWindow({
      url,
      external,
    });
  }

  async closeWindow(): Promise<void> {
    if (!this.initialized) {
      throw new Error("LIFF not initialized");
    }

    liff.closeWindow();
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async sendMessages(messages: any[]): Promise<void> {
    if (!this.initialized) {
      throw new Error("LIFF not initialized");
    }

    if (!this.isApiAvailable("sendMessages")) {
      throw new Error("sendMessages API is not available");
    }

    try {
      await liff.sendMessages(messages);
    } catch (error) {
      console.error("Failed to send messages:", error);
      throw error;
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async shareTargetPicker(messages: any[]): Promise<void> {
    if (!this.initialized) {
      throw new Error("LIFF not initialized");
    }

    if (!this.isApiAvailable("shareTargetPicker")) {
      throw new Error("shareTargetPicker API is not available");
    }

    try {
      await liff.shareTargetPicker(messages);
    } catch (error) {
      console.error("Failed to share target picker:", error);
      throw error;
    }
  }
}

// Export singleton instance
export const liffService = new LiffService();

// Hook for React components
export function useLiff() {
  return liffService;
}
