import { Noto_Sans_Thai } from 'next/font/google'

// Configure Noto Sans Thai font
export const notoSansThai = Noto_Sans_Thai({
  subsets: ['thai', 'latin'],
  weight: ['300', '400', '500', '600', '700'],
  variable: '--font-noto-sans-thai',
  display: 'swap',
  fallback: [
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Oxygen',
    'Ubuntu',
    'Cantarell',
    'Open Sans',
    'Helvetica Neue',
    'sans-serif'
  ]
})

// Export the font class name for use in components
export const fontClassName = notoSansThai.className
export const fontVariable = notoSansThai.variable
