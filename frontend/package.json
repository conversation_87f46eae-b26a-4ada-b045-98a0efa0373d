{"name": "bodylab-reservation-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@heroui/react": "^2.7.11", "@line/liff": "^2.27.0", "framer-motion": "^12.23.3", "lucide-react": "^0.534.0", "next": "15.3.5", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "^5"}}