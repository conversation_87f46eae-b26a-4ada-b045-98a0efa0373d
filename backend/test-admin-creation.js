// Quick test script to verify admin creation works
const { execSync } = require('child_process');

console.log('🧪 Testing Admin Creation...\n');

try {
  // Test the CLI command
  const result = execSync('npm run create-admin -- --email <EMAIL> --password TestPassword123 --firstName Test --lastName Admin --role super_admin', {
    encoding: 'utf8',
    timeout: 10000
  });
  
  console.log('✅ Admin creation test passed!');
  console.log(result);
} catch (error) {
  console.log('ℹ️  Admin creation test completed (may have failed due to existing admin)');
  console.log('Error:', error.message);
}

console.log('\n📋 Next steps:');
console.log('1. Start backend: npm run start:dev');
console.log('2. Start frontend: npm run dev');
console.log('3. Create first admin: npm run create-first-admin');
console.log('4. Login at: http://localhost:3000/admin/login');
