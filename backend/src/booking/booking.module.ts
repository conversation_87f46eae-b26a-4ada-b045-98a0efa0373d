import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BookingController } from './booking.controller';
import { BookingService } from './booking.service';
import { Booking } from '../entities/booking.entity';
import { Course } from '../entities/course.entity';
import { User } from '../entities/user.entity';
import { Branch } from '../entities/branch.entity';
import { TrainerSchedule } from '../entities/trainer-schedule.entity';
import { ActivityLog } from '../entities/activity-log.entity';
import { AuthModule } from '../auth/auth.module';
import { CalendarModule } from '../calendar/calendar.module';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Booking,
      Course,
      User,
      Branch,
      TrainerSchedule,
      ActivityLog,
    ]),
    AuthModule,
    CalendarModule,
    NotificationModule,
  ],
  controllers: [BookingController],
  providers: [BookingService],
  exports: [BookingService],
})
export class BookingModule {}
