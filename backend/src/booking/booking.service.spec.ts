import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { BookingService } from './booking.service';
import { Booking, BookingStatus } from '../entities/booking.entity';
import { Course, CourseStatus } from '../entities/course.entity';
import { User, UserRole } from '../entities/user.entity';
import { Branch } from '../entities/branch.entity';
import { TrainerSchedule } from '../entities/trainer-schedule.entity';
import { ActivityLog } from '../entities/activity-log.entity';
import { CalendarService } from '../calendar/calendar.service';
import { NotificationService } from '../notification/notification.service';

describe('BookingService', () => {
  let service: BookingService;
  let bookingRepository: any;
  let courseRepository: any;
  let userRepository: any;
  let calendarService: CalendarService;
  let notificationService: NotificationService;

  const mockCustomer = {
    id: 'customer-1',
    displayName: 'Customer',
    role: UserRole.CUSTOMER,
  };

  const mockTrainer = {
    id: 'trainer-1',
    displayName: 'Trainer',
    role: UserRole.TRAINER,
    isActive: true,
  };

  const mockCourse = {
    id: 'course-1',
    name: 'Basic Pilates',
    remainingSessions: 5,
    totalSessions: 10,
    usedSessions: 5,
    status: CourseStatus.ACTIVE,
    expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    customer: mockCustomer,
    branch: { id: 'branch-1', name: 'Main Branch' },
  };

  const mockBooking = {
    id: 'booking-1',
    customer: mockCustomer,
    trainer: mockTrainer,
    course: mockCourse,
    scheduledDateTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // tomorrow
    durationMinutes: 60,
    status: BookingStatus.SCHEDULED,
    frontConfirmed: false,
    trainerConfirmed: false,
  };

  const mockRepositories = {
    booking: {
      create: jest.fn(),
      save: jest.fn(),
      findOne: jest.fn(),
      find: jest.fn(),
      createQueryBuilder: jest.fn(),
    },
    course: {
      findOne: jest.fn(),
      save: jest.fn(),
    },
    user: {
      findOne: jest.fn(),
    },
    branch: {},
    schedule: {},
    activityLog: {
      create: jest.fn(),
      save: jest.fn(),
    },
  };

  const mockCalendarService = {
    createBookingEvent: jest.fn(),
    updateBookingEvent: jest.fn(),
    deleteBookingEvent: jest.fn(),
  };

  const mockNotificationService = {
    notifyBookingCreated: jest.fn(),
    notifySessionCompleted: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BookingService,
        {
          provide: getRepositoryToken(Booking),
          useValue: mockRepositories.booking,
        },
        {
          provide: getRepositoryToken(Course),
          useValue: mockRepositories.course,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockRepositories.user,
        },
        {
          provide: getRepositoryToken(Branch),
          useValue: mockRepositories.branch,
        },
        {
          provide: getRepositoryToken(TrainerSchedule),
          useValue: mockRepositories.schedule,
        },
        {
          provide: getRepositoryToken(ActivityLog),
          useValue: mockRepositories.activityLog,
        },
        {
          provide: CalendarService,
          useValue: mockCalendarService,
        },
        {
          provide: NotificationService,
          useValue: mockNotificationService,
        },
      ],
    }).compile();

    service = module.get<BookingService>(BookingService);
    bookingRepository = module.get(getRepositoryToken(Booking));
    courseRepository = module.get(getRepositoryToken(Course));
    userRepository = module.get(getRepositoryToken(User));
    calendarService = module.get<CalendarService>(CalendarService);
    notificationService = module.get<NotificationService>(NotificationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createBooking', () => {
    const createBookingDto = {
      courseId: 'course-1',
      trainerId: 'trainer-1',
      scheduledDateTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
      durationMinutes: 60,
    };

    it('should create booking successfully', async () => {
      courseRepository.findOne.mockResolvedValue(mockCourse);
      userRepository.findOne.mockResolvedValue(mockTrainer);
      jest.spyOn(service, 'checkTrainerAvailability').mockResolvedValue(true);
      bookingRepository.create.mockReturnValue(mockBooking);
      bookingRepository.save.mockResolvedValue(mockBooking);
      bookingRepository.findOne.mockResolvedValue(mockBooking);
      courseRepository.save.mockResolvedValue(mockCourse);
      mockCalendarService.createBookingEvent.mockResolvedValue('event-123');
      mockNotificationService.notifyBookingCreated.mockResolvedValue(undefined);

      const result = await service.createBooking('customer-1', createBookingDto);

      expect(result).toEqual(mockBooking);
      expect(bookingRepository.create).toHaveBeenCalled();
      expect(bookingRepository.save).toHaveBeenCalled();
      expect(mockCalendarService.createBookingEvent).toHaveBeenCalled();
      expect(mockNotificationService.notifyBookingCreated).toHaveBeenCalled();
    });

    it('should throw NotFoundException if course not found', async () => {
      courseRepository.findOne.mockResolvedValue(null);

      await expect(
        service.createBooking('customer-1', createBookingDto),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException if course is not active', async () => {
      const inactiveCourse = { ...mockCourse, status: CourseStatus.EXPIRED };
      courseRepository.findOne.mockResolvedValue(inactiveCourse);

      await expect(
        service.createBooking('customer-1', createBookingDto),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if no remaining sessions', async () => {
      const noSessionsCourse = { ...mockCourse, remainingSessions: 0 };
      courseRepository.findOne.mockResolvedValue(noSessionsCourse);

      await expect(
        service.createBooking('customer-1', createBookingDto),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if trainer not available', async () => {
      courseRepository.findOne.mockResolvedValue(mockCourse);
      userRepository.findOne.mockResolvedValue(mockTrainer);
      jest.spyOn(service, 'checkTrainerAvailability').mockResolvedValue(false);

      await expect(
        service.createBooking('customer-1', createBookingDto),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('cancelBooking', () => {
    it('should cancel booking successfully', async () => {
      const cancelledBooking = { ...mockBooking, status: BookingStatus.CANCELLED };
      bookingRepository.findOne.mockResolvedValue(mockBooking);
      bookingRepository.save.mockResolvedValue(cancelledBooking);
      courseRepository.save.mockResolvedValue(mockCourse);
      mockCalendarService.deleteBookingEvent.mockResolvedValue(true);

      const result = await service.cancelBooking(
        'booking-1',
        'customer-1',
        UserRole.CUSTOMER,
        'Changed plans',
      );

      expect(result.status).toBe(BookingStatus.CANCELLED);
      expect(result.cancellationReason).toBe('Changed plans');
      expect(mockCalendarService.deleteBookingEvent).toHaveBeenCalled();
    });

    it('should throw NotFoundException if booking not found', async () => {
      bookingRepository.findOne.mockResolvedValue(null);

      await expect(
        service.cancelBooking('booking-1', 'customer-1', UserRole.CUSTOMER),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException if booking already cancelled', async () => {
      const cancelledBooking = { ...mockBooking, status: BookingStatus.CANCELLED };
      bookingRepository.findOne.mockResolvedValue(cancelledBooking);

      await expect(
        service.cancelBooking('booking-1', 'customer-1', UserRole.CUSTOMER),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('confirmSession', () => {
    it('should confirm session from front desk', async () => {
      const confirmedBooking = { ...mockBooking, frontConfirmed: true };
      bookingRepository.findOne.mockResolvedValue(mockBooking);
      bookingRepository.save.mockResolvedValue(confirmedBooking);

      const result = await service.confirmSession(
        'booking-1',
        'admin-1',
        UserRole.BRANCH_ADMIN,
        'front',
      );

      expect(result.frontConfirmed).toBe(true);
    });

    it('should mark as completed when both confirmations done', async () => {
      const partiallyConfirmed = { ...mockBooking, frontConfirmed: true };
      const fullyConfirmed = {
        ...partiallyConfirmed,
        trainerConfirmed: true,
        status: BookingStatus.COMPLETED,
      };

      bookingRepository.findOne.mockResolvedValue(partiallyConfirmed);
      bookingRepository.save.mockResolvedValue(fullyConfirmed);
      mockCalendarService.updateBookingEvent.mockResolvedValue(true);
      mockNotificationService.notifySessionCompleted.mockResolvedValue(undefined);

      const result = await service.confirmSession(
        'booking-1',
        'trainer-1',
        UserRole.TRAINER,
        'trainer',
        { sessionNotes: 'Great session!' },
      );

      expect(result.status).toBe(BookingStatus.COMPLETED);
      expect(result.sessionNotes).toBe('Great session!');
      expect(mockNotificationService.notifySessionCompleted).toHaveBeenCalled();
    });
  });
});
