import {
  Injectable,
  BadRequestException,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThan } from 'typeorm';
import { Booking, BookingStatus } from '../entities/booking.entity';
import { Course, CourseStatus } from '../entities/course.entity';
import { User, UserRole } from '../entities/user.entity';
import { Branch } from '../entities/branch.entity';
import {
  TrainerSchedule,
  ScheduleStatus,
} from '../entities/trainer-schedule.entity';
import { ActivityLog, ActivityType } from '../entities/activity-log.entity';
import { CalendarService } from '../calendar/calendar.service';
import { NotificationService } from '../notification/notification.service';

export class CreateBookingDto {
  courseId: number;
  trainerId: number;
  scheduledDateTime: Date;
  durationMinutes?: number;
  notes?: string;
}

export class UpdateBookingDto {
  scheduledDateTime?: Date;
  durationMinutes?: number;
  notes?: string;
  cancellationReason?: string;
}

export class ConfirmSessionDto {
  sessionNotes?: string;
  bodyMetrics?: any;
  trainerRecommendations?: string;
}

@Injectable()
export class BookingService {
  constructor(
    @InjectRepository(Booking)
    private bookingRepository: Repository<Booking>,
    @InjectRepository(Course)
    private courseRepository: Repository<Course>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Branch)
    private branchRepository: Repository<Branch>,
    @InjectRepository(TrainerSchedule)
    private scheduleRepository: Repository<TrainerSchedule>,
    @InjectRepository(ActivityLog)
    private activityLogRepository: Repository<ActivityLog>,
    private calendarService: CalendarService,
    private notificationService: NotificationService,
  ) {}

  async createBooking(
    customerId: number,
    createBookingDto: CreateBookingDto,
  ): Promise<Booking> {
    const {
      courseId,
      trainerId,
      scheduledDateTime,
      durationMinutes = 60,
      notes,
    } = createBookingDto;

    // Validate course
    const course = await this.courseRepository.findOne({
      where: { id: courseId, customer: { id: customerId } },
      relations: ['customer', 'branch'],
    });

    if (!course) {
      throw new NotFoundException(
        'Course not found or does not belong to customer',
      );
    }

    if (course.status !== CourseStatus.ACTIVE) {
      throw new BadRequestException('Course is not active');
    }

    if (course.remainingSessions <= 0) {
      throw new BadRequestException('No remaining sessions in course');
    }

    // Check if course is expired
    if (new Date() > course.expiryDate) {
      throw new BadRequestException('Course has expired');
    }

    // Validate trainer
    const trainer = await this.userRepository.findOne({
      where: { id: trainerId, role: UserRole.TRAINER, isActive: true },
      relations: ['branch'],
    });

    if (!trainer) {
      throw new NotFoundException('Trainer not found or inactive');
    }

    // Check if booking is in advance only
    const now = new Date();
    const bookingTime = new Date(scheduledDateTime);
    if (bookingTime <= now) {
      throw new BadRequestException('Booking must be made in advance');
    }

    // Check trainer availability
    const isAvailable = await this.checkTrainerAvailability(
      trainerId,
      bookingTime,
      durationMinutes,
    );
    if (!isAvailable) {
      throw new BadRequestException(
        'Trainer is not available at the requested time',
      );
    }

    // Create booking
    const booking = this.bookingRepository.create({
      customer: { id: customerId },
      trainer: { id: trainerId },
      course: { id: courseId },
      branch: course.branch,
      scheduledDateTime: bookingTime,
      durationMinutes,
      notes,
      status: BookingStatus.SCHEDULED,
    });

    const savedBooking = await this.bookingRepository.save(booking);

    // Update course remaining sessions
    course.remainingSessions -= 1;
    course.usedSessions += 1;
    await this.courseRepository.save(course);

    // Get the full booking with relations for calendar integration
    const result = await this.bookingRepository.findOne({
      where: { id: savedBooking.id },
      relations: ['customer', 'trainer', 'course', 'branch'],
    });

    if (!result) {
      throw new NotFoundException('Booking not found after creation');
    }

    // Create Google Calendar event
    try {
      const eventId = await this.calendarService.createBookingEvent(result);
      if (eventId) {
        result.googleCalendarEventId = eventId;
        await this.bookingRepository.save(result);
      }
    } catch (error) {
      console.error('Failed to create calendar event:', error);
      // Don't fail the booking if calendar creation fails
    }

    // Send notifications
    try {
      await this.notificationService.notifyBookingCreated(result);
    } catch (error) {
      console.error('Failed to send booking notifications:', error);
      // Don't fail the booking if notifications fail
    }

    // Log activity
    await this.logActivity(
      ActivityType.BOOKING_CREATED,
      `Booking created for ${course.customer.displayName} with trainer ${trainer.displayName}`,
      course.customer,
      { bookingId: savedBooking.id, trainerId, scheduledDateTime },
    );

    return result;
  }

  async checkTrainerAvailability(
    trainerId: number,
    dateTime: Date,
    durationMinutes: number,
  ): Promise<boolean> {
    const endTime = new Date(dateTime.getTime() + durationMinutes * 60000);

    // Check for existing bookings
    const existingBookings = await this.bookingRepository.find({
      where: {
        trainer: { id: trainerId },
        scheduledDateTime: Between(
          new Date(dateTime.getTime() - 60 * 60000), // 1 hour before
          new Date(endTime.getTime() + 60 * 60000), // 1 hour after
        ),
        status: BookingStatus.SCHEDULED,
      },
    });

    return existingBookings.length === 0;
  }

  async cancelBooking(
    bookingId: number,
    userId: number,
    userRole: UserRole,
    cancellationReason?: string,
  ): Promise<Booking> {
    const booking = await this.bookingRepository.findOne({
      where: { id: bookingId },
      relations: ['customer', 'trainer', 'course', 'branch'],
    });

    if (!booking) {
      throw new NotFoundException('Booking not found');
    }

    // Check permissions
    if (userRole === UserRole.CUSTOMER && booking.customer.id !== userId) {
      throw new ForbiddenException('You can only cancel your own bookings');
    }

    if (booking.status === BookingStatus.CANCELLED) {
      throw new BadRequestException('Booking is already cancelled');
    }

    if (booking.status === BookingStatus.COMPLETED) {
      throw new BadRequestException('Cannot cancel completed booking');
    }

    // Update booking status
    booking.status = BookingStatus.CANCELLED;
    booking.cancellationReason = cancellationReason;

    const updatedBooking = await this.bookingRepository.save(booking);

    // Delete Google Calendar event
    if (booking.googleCalendarEventId) {
      try {
        await this.calendarService.deleteBookingEvent(
          booking.googleCalendarEventId,
        );
      } catch (error) {
        console.error('Failed to delete calendar event:', error);
        // Don't fail the cancellation if calendar deletion fails
      }
    }

    // Restore course session
    const course = booking.course;
    course.remainingSessions += 1;
    course.usedSessions -= 1;
    await this.courseRepository.save(course);

    // Log activity
    await this.logActivity(
      ActivityType.BOOKING_CANCELLED,
      `Booking cancelled: ${booking.customer.displayName} with trainer ${booking.trainer.displayName}`,
      booking.customer,
      { bookingId, cancellationReason },
    );

    return updatedBooking;
  }

  async confirmSession(
    bookingId: number,
    userId: number,
    userRole: UserRole,
    confirmationType: 'front' | 'trainer',
    confirmSessionDto?: ConfirmSessionDto,
  ): Promise<Booking> {
    const booking = await this.bookingRepository.findOne({
      where: { id: bookingId },
      relations: ['customer', 'trainer', 'course', 'branch'],
    });

    if (!booking) {
      throw new NotFoundException('Booking not found');
    }

    if (booking.status !== BookingStatus.SCHEDULED) {
      throw new BadRequestException('Booking is not in scheduled status');
    }

    // Check permissions
    if (
      confirmationType === 'trainer' &&
      booking.trainer.id !== userId &&
      userRole !== UserRole.BRANCH_ADMIN
    ) {
      throw new ForbiddenException(
        'Only the assigned trainer or branch admin can confirm as trainer',
      );
    }

    if (confirmationType === 'front' && userRole !== UserRole.BRANCH_ADMIN) {
      throw new ForbiddenException(
        'Only branch admin can confirm from front desk',
      );
    }

    // Update confirmation
    if (confirmationType === 'front') {
      booking.frontConfirmed = true;
      booking.frontConfirmedAt = new Date();
    } else {
      booking.trainerConfirmed = true;
      booking.trainerConfirmedAt = new Date();

      if (confirmSessionDto) {
        booking.sessionNotes = confirmSessionDto.sessionNotes;
        booking.bodyMetrics = confirmSessionDto.bodyMetrics;
        booking.trainerRecommendations =
          confirmSessionDto.trainerRecommendations;
      }
    }

    // If both confirmations are done, mark as completed
    if (booking.frontConfirmed && booking.trainerConfirmed) {
      booking.status = BookingStatus.COMPLETED;
    }

    const updatedBooking = await this.bookingRepository.save(booking);

    // Send completion notifications if session is completed
    if (updatedBooking.status === BookingStatus.COMPLETED) {
      try {
        await this.notificationService.notifySessionCompleted(updatedBooking);
      } catch (error) {
        console.error('Failed to send completion notifications:', error);
        // Don't fail the confirmation if notifications fail
      }
    }

    // Update Google Calendar event with session details
    if (
      booking.googleCalendarEventId &&
      booking.frontConfirmed &&
      booking.trainerConfirmed
    ) {
      try {
        await this.calendarService.updateBookingEvent(
          booking.googleCalendarEventId,
          updatedBooking,
        );
      } catch (error) {
        console.error('Failed to update calendar event:', error);
        // Don't fail the confirmation if calendar update fails
      }
    }

    // Log activity
    await this.logActivity(
      ActivityType.SESSION_CONFIRMED,
      `Session confirmed by ${confirmationType}: ${booking.customer.displayName} with trainer ${booking.trainer.displayName}`,
      booking.customer,
      {
        bookingId,
        confirmationType,
        confirmed:
          confirmationType === 'front'
            ? booking.frontConfirmed
            : booking.trainerConfirmed,
      },
    );

    return updatedBooking;
  }

  async getBookings(
    userId: number,
    userRole: UserRole,
    filters?: {
      status?: BookingStatus;
      trainerId?: number;
      customerId?: number;
      branchId?: number;
      startDate?: Date;
      endDate?: Date;
    },
  ): Promise<Booking[]> {
    const queryBuilder = this.bookingRepository
      .createQueryBuilder('booking')
      .leftJoinAndSelect('booking.customer', 'customer')
      .leftJoinAndSelect('booking.trainer', 'trainer')
      .leftJoinAndSelect('booking.course', 'course')
      .leftJoinAndSelect('booking.branch', 'branch');

    // Apply role-based filtering
    if (userRole === UserRole.CUSTOMER) {
      queryBuilder.where('customer.id = :userId', { userId });
    } else if (userRole === UserRole.TRAINER) {
      queryBuilder.where('trainer.id = :userId', { userId });
    }

    // Apply additional filters
    if (filters) {
      if (filters.status) {
        queryBuilder.andWhere('booking.status = :status', {
          status: filters.status,
        });
      }
      if (filters.trainerId) {
        queryBuilder.andWhere('trainer.id = :trainerId', {
          trainerId: filters.trainerId,
        });
      }
      if (filters.customerId) {
        queryBuilder.andWhere('customer.id = :customerId', {
          customerId: filters.customerId,
        });
      }
      if (filters.branchId) {
        queryBuilder.andWhere('branch.id = :branchId', {
          branchId: filters.branchId,
        });
      }
      if (filters.startDate) {
        queryBuilder.andWhere('booking.scheduledDateTime >= :startDate', {
          startDate: filters.startDate,
        });
      }
      if (filters.endDate) {
        queryBuilder.andWhere('booking.scheduledDateTime <= :endDate', {
          endDate: filters.endDate,
        });
      }
    }

    queryBuilder.orderBy('booking.scheduledDateTime', 'ASC');

    return queryBuilder.getMany();
  }

  async getBookingById(
    bookingId: number,
    userId: number,
    userRole: UserRole,
  ): Promise<Booking> {
    const booking = await this.bookingRepository.findOne({
      where: { id: bookingId },
      relations: ['customer', 'trainer', 'course', 'branch'],
    });

    if (!booking) {
      throw new NotFoundException('Booking not found');
    }

    // Check permissions
    if (userRole === UserRole.CUSTOMER && booking.customer.id !== userId) {
      throw new ForbiddenException('You can only view your own bookings');
    }

    if (userRole === UserRole.TRAINER && booking.trainer.id !== userId) {
      throw new ForbiddenException(
        'You can only view your own training sessions',
      );
    }

    return booking;
  }

  private async logActivity(
    activityType: ActivityType,
    description: string,
    user?: User,
    metadata?: any,
  ): Promise<void> {
    const log = this.activityLogRepository.create({
      activityType,
      description,
      user,
      metadata,
    });
    await this.activityLogRepository.save(log);
  }
}
