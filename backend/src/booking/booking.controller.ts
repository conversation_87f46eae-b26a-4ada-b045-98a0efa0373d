import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import {
  BookingService,
  CreateBookingDto,
  UpdateBookingDto,
  ConfirmSessionDto,
} from './booking.service';
import { GetUser } from '../auth/decorators/get-user.decorator';
import { Roles } from '../auth/decorators/roles.decorator';
import { RolesGuard } from '../auth/guards/roles.guard';
import { User, UserRole } from '../entities/user.entity';
import { BookingStatus } from '../entities/booking.entity';

@Controller('bookings')
@UseGuards(AuthGuard('jwt'))
export class BookingController {
  constructor(private bookingService: BookingService) {}

  @Post()
  @Roles(UserRole.CUSTOMER)
  @UseGuards(RolesGuard)
  async createBooking(
    @GetUser() user: User,
    @Body() createBookingDto: CreateBookingDto,
  ) {
    const booking = await this.bookingService.createBooking(
      user.id,
      createBookingDto,
    );
    return {
      success: true,
      data: booking,
      message: 'Booking created successfully',
    };
  }

  @Get()
  async getBookings(
    @GetUser() user: User,
    @Query('status') status?: BookingStatus,
    @Query('trainerId') trainerId?: string,
    @Query('customerId') customerId?: string,
    @Query('branchId') branchId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const filters = {
      status,
      trainerId: trainerId ? parseInt(trainerId) : undefined,
      customerId: customerId ? parseInt(customerId) : undefined,
      branchId: branchId ? parseInt(branchId) : undefined,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
    };

    const bookings = await this.bookingService.getBookings(
      user.id,
      user.role,
      filters,
    );
    return {
      success: true,
      data: bookings,
    };
  }

  @Get(':id')
  async getBookingById(
    @GetUser() user: User,
    @Param('id', ParseIntPipe) bookingId: number,
  ) {
    const booking = await this.bookingService.getBookingById(
      bookingId,
      user.id,
      user.role,
    );
    return {
      success: true,
      data: booking,
    };
  }

  @Put(':id/cancel')
  async cancelBooking(
    @GetUser() user: User,
    @Param('id', ParseIntPipe) bookingId: number,
    @Body('cancellationReason') cancellationReason?: string,
  ) {
    const booking = await this.bookingService.cancelBooking(
      bookingId,
      user.id,
      user.role,
      cancellationReason,
    );
    return {
      success: true,
      data: booking,
      message: 'Booking cancelled successfully',
    };
  }

  @Put(':id/confirm/front')
  @Roles(UserRole.BRANCH_ADMIN)
  @UseGuards(RolesGuard)
  async confirmSessionFront(
    @GetUser() user: User,
    @Param('id', ParseIntPipe) bookingId: number,
  ) {
    const booking = await this.bookingService.confirmSession(
      bookingId,
      user.id,
      user.role,
      'front',
    );
    return {
      success: true,
      data: booking,
      message: 'Session confirmed by front desk',
    };
  }

  @Put(':id/confirm/trainer')
  @Roles(UserRole.TRAINER, UserRole.BRANCH_ADMIN)
  @UseGuards(RolesGuard)
  async confirmSessionTrainer(
    @GetUser() user: User,
    @Param('id', ParseIntPipe) bookingId: number,
    @Body() confirmSessionDto: ConfirmSessionDto,
  ) {
    const booking = await this.bookingService.confirmSession(
      bookingId,
      user.id,
      user.role,
      'trainer',
      confirmSessionDto,
    );
    return {
      success: true,
      data: booking,
      message: 'Session confirmed by trainer',
    };
  }
}
