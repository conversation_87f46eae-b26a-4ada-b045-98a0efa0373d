import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
} from 'typeorm';
import { User } from './user.entity';
import { Branch } from './branch.entity';

export enum ScheduleStatus {
  AVAILABLE = 'available',
  BOOKED = 'booked',
  BREAK = 'break',
  UNAVAILABLE = 'unavailable',
}

@Entity('trainer_schedules')
export class TrainerSchedule {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'date' })
  date: Date;

  @Column({ type: 'time' })
  startTime: string;

  @Column({ type: 'time' })
  endTime: string;

  @Column({
    type: 'enum',
    enum: ScheduleStatus,
    default: ScheduleStatus.AVAILABLE,
  })
  status: ScheduleStatus;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ default: false })
  isRecurring: boolean;

  @Column({ type: 'json', nullable: true })
  recurringDays: string[]; // ['monday', 'tuesday', etc.]

  @ManyToOne(() => User, (user) => user.id)
  trainer: User;

  @ManyToOne(() => Branch, (branch) => branch.id)
  branch: Branch;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
