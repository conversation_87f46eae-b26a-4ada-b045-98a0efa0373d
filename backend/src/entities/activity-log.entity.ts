import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
} from 'typeorm';
import { User } from './user.entity';
import { Admin } from './admin.entity';

export enum ActivityType {
  USER_REGISTRATION = 'user_registration',
  COURSE_PURCHASE = 'course_purchase',
  BOOKING_CREATED = 'booking_created',
  BOOKING_CANCELLED = 'booking_cancelled',
  SESSION_COMPLETED = 'session_completed',
  SESSION_CONFIRMED = 'session_confirmed',
  COURSE_TRANSFERRED = 'course_transferred',
  COURSE_MODIFIED = 'course_modified',
  TRAINER_SCHEDULE_UPDATED = 'trainer_schedule_updated',
  LOGIN = 'login',
  LOGOUT = 'logout',
}

@Entity('activity_logs')
export class ActivityLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: ActivityType,
  })
  activityType: ActivityType;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'json', nullable: true })
  metadata: any; // Additional data related to the activity

  @Column({ nullable: true })
  ipAddress: string;

  @Column({ nullable: true })
  userAgent: string;

  @ManyToOne(() => User, (user) => user.activityLogs, { nullable: true })
  user?: User;

  @ManyToOne(() => Admin, (admin) => admin.activityLogs, { nullable: true })
  admin?: Admin;

  @CreateDateColumn()
  createdAt: Date;
}
