import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { Branch } from './branch.entity';
import { Booking } from './booking.entity';
import { Course } from './course.entity';
import { ActivityLog } from './activity-log.entity';
import { UserBranchAccess } from './user-branch-access.entity';

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  BRANCH_ADMIN = 'branch_admin',
  TRAINER = 'trainer',
  CUSTOMER = 'customer',
}

export enum MembershipLevel {
  BRONZE = 'bronze',
  SILVER = 'silver',
  GOLD = 'gold',
  PLATINUM = 'platinum',
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  lineUserId: string;

  @Column()
  displayName: string;

  @Column({ nullable: true })
  email?: string;

  @Column({ nullable: true })
  phoneNumber?: string;

  @Column({ nullable: true })
  profilePictureUrl?: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.CUSTOMER,
  })
  role: UserRole;

  @Column({
    type: 'enum',
    enum: MembershipLevel,
    default: MembershipLevel.BRONZE,
  })
  membershipLevel: MembershipLevel;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  totalSpent: number;

  @Column({ default: false })
  hasGivenConsent: boolean;

  @Column({ default: true })
  isActive: boolean;

  @ManyToOne(() => Branch, (branch) => branch.users, { nullable: true })
  branch: Branch;

  @OneToMany(() => Booking, (booking) => booking.customer)
  bookings: Booking[];

  @OneToMany(() => Booking, (booking) => booking.trainer)
  trainerBookings: Booking[];

  @OneToMany(() => Course, (course) => course.customer)
  courses: Course[];

  @OneToMany(() => ActivityLog, (log) => log.user)
  activityLogs: ActivityLog[];

  @OneToMany(() => UserBranchAccess, (access) => access.user)
  branchAccess: UserBranchAccess[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
