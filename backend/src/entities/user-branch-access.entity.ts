import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Branch } from './branch.entity';
import { Admin } from './admin.entity';

export enum AccessLevel {
  RESERVATION = 'reservation', // Can make reservations at this branch
  FULL_ACCESS = 'full_access', // Can make reservations and use all services at this branch
}

@Entity('user_branch_access')
@Index(['user', 'branch'], { unique: true }) // Prevent duplicate access records
export class UserBranchAccess {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => User, (user) => user.branchAccess, { onDelete: 'CASCADE' })
  user: User;

  @ManyToOne(() => Branch, (branch) => branch.userAccess, {
    onDelete: 'CASCADE',
  })
  branch: Branch;

  @Column({
    type: 'enum',
    enum: AccessLevel,
    default: AccessLevel.FULL_ACCESS,
  })
  accessLevel: AccessLevel;

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ type: 'datetime', nullable: true })
  grantedAt?: Date;

  @Column({ type: 'datetime', nullable: true })
  revokedAt?: Date;

  @ManyToOne(() => Admin, { nullable: true })
  grantedBy?: Admin; // Admin who granted the access

  @ManyToOne(() => Admin, { nullable: true })
  revokedBy?: Admin; // Admin who revoked the access

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
