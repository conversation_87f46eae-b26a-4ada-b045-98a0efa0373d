import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { User } from './user.entity';
import { Booking } from './booking.entity';
import { Course } from './course.entity';
import { UserBranchAccess } from './user-branch-access.entity';

@Entity('branches')
export class Branch {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  address: string;

  @Column({ nullable: true })
  phoneNumber: string;

  @Column({ nullable: true })
  email: string;

  @Column({ type: 'time' })
  openTime: string;

  @Column({ type: 'time' })
  closeTime: string;

  @Column({ type: 'json', nullable: true })
  operatingDays: string[]; // ['monday', 'tuesday', etc.]

  @Column({ default: true })
  isActive: boolean;

  @OneToMany(() => User, (user) => user.branch)
  users: User[];

  @OneToMany(() => Booking, (booking) => booking.branch)
  bookings: Booking[];

  @OneToMany(() => Course, (course) => course.branch)
  courses: Course[];

  @OneToMany(() => UserBranchAccess, (access) => access.branch)
  userAccess: UserBranchAccess[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
