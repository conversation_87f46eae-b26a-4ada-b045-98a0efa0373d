import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
} from 'typeorm';
import { User } from './user.entity';
import { Branch } from './branch.entity';
import { Course } from './course.entity';

export enum BookingStatus {
  SCHEDULED = 'scheduled',
  CONFIRMED = 'confirmed',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  NO_SHOW = 'no_show',
}

@Entity('bookings')
export class Booking {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'datetime' })
  scheduledDateTime: Date;

  @Column({ type: 'int', default: 60 })
  durationMinutes: number;

  @Column({
    type: 'enum',
    enum: BookingStatus,
    default: BookingStatus.SCHEDULED,
  })
  status: BookingStatus;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ type: 'text', nullable: true })
  cancellationReason?: string;

  @Column({ default: false })
  frontConfirmed: boolean;

  @Column({ default: false })
  trainerConfirmed: boolean;

  @Column({ type: 'datetime', nullable: true })
  frontConfirmedAt?: Date;

  @Column({ type: 'datetime', nullable: true })
  trainerConfirmedAt?: Date;

  @Column({ type: 'text', nullable: true })
  sessionNotes?: string; // Notes from trainer about the session

  @Column({ type: 'json', nullable: true })
  bodyMetrics?: any; // Body measurements recorded by trainer

  @Column({ type: 'text', nullable: true })
  trainerRecommendations?: string;

  @Column({ nullable: true })
  googleCalendarEventId: string;

  @ManyToOne(() => User, (user) => user.bookings)
  customer: User;

  @ManyToOne(() => User, (user) => user.trainerBookings)
  trainer: User;

  @ManyToOne(() => Branch, (branch) => branch.bookings)
  branch: Branch;

  @ManyToOne(() => Course, (course) => course.bookings)
  course: Course;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
