import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { google } from 'googleapis';
import { Booking } from '../entities/booking.entity';

@Injectable()
export class CalendarService {
  private readonly logger = new Logger(CalendarService.name);
  private calendar;

  constructor(private configService: ConfigService) {
    this.initializeCalendar();
  }

  private initializeCalendar() {
    try {
      const auth = new google.auth.OAuth2(
        this.configService.get('GOOGLE_CLIENT_ID'),
        this.configService.get('GOOGLE_CLIENT_SECRET'),
        this.configService.get('GOOGLE_REDIRECT_URI'),
      );

      // In production, you would store and refresh tokens properly
      // For now, we'll use a service account or stored refresh token
      this.calendar = google.calendar({ version: 'v3', auth });
    } catch (error) {
      this.logger.error('Failed to initialize Google Calendar', error);
    }
  }

  async createBookingEvent(booking: Booking): Promise<string | null> {
    if (!this.calendar) {
      this.logger.warn('Google Calendar not initialized');
      return null;
    }

    try {
      // Format: <ชื่อครู> 5/20 <ชื่อลูกค้า> <เบอร์โทรลูกค้า>
      const eventTitle = `${booking.trainer.displayName} ${booking.course.usedSessions + 1}/${booking.course.totalSessions} ${booking.customer.displayName} ${booking.customer.phoneNumber || 'N/A'}`;

      const startTime = new Date(booking.scheduledDateTime);
      const endTime = new Date(startTime.getTime() + booking.durationMinutes * 60000);

      const event = {
        summary: eventTitle,
        description: `
Pilates Session Details:
- Course: ${booking.course.name}
- Customer: ${booking.customer.displayName}
- Trainer: ${booking.trainer.displayName}
- Branch: ${booking.branch.name}
- Session: ${booking.course.usedSessions + 1} of ${booking.course.totalSessions}
- Duration: ${booking.durationMinutes} minutes
${booking.notes ? `- Notes: ${booking.notes}` : ''}

Booking ID: ${booking.id}
        `.trim(),
        start: {
          dateTime: startTime.toISOString(),
          timeZone: 'Asia/Bangkok',
        },
        end: {
          dateTime: endTime.toISOString(),
          timeZone: 'Asia/Bangkok',
        },
        location: booking.branch.address,
        attendees: [
          {
            email: booking.customer.email,
            displayName: booking.customer.displayName,
          },
          {
            email: booking.trainer.email,
            displayName: booking.trainer.displayName,
          },
        ],
        reminders: {
          useDefault: false,
          overrides: [
            { method: 'email', minutes: 24 * 60 }, // 1 day before
            { method: 'popup', minutes: 60 }, // 1 hour before
            { method: 'popup', minutes: 15 }, // 15 minutes before
          ],
        },
      };

      const response = await this.calendar.events.insert({
        calendarId: 'primary',
        requestBody: event,
      });

      this.logger.log(`Created calendar event: ${response.data.id}`);
      return response.data.id;
    } catch (error) {
      this.logger.error('Failed to create calendar event', error);
      return null;
    }
  }

  async updateBookingEvent(eventId: string, booking: Booking): Promise<boolean> {
    if (!this.calendar || !eventId) {
      return false;
    }

    try {
      const eventTitle = `${booking.trainer.displayName} ${booking.course.usedSessions + 1}/${booking.course.totalSessions} ${booking.customer.displayName} ${booking.customer.phoneNumber || 'N/A'}`;

      const startTime = new Date(booking.scheduledDateTime);
      const endTime = new Date(startTime.getTime() + booking.durationMinutes * 60000);

      const event = {
        summary: eventTitle,
        description: `
Pilates Session Details:
- Course: ${booking.course.name}
- Customer: ${booking.customer.displayName}
- Trainer: ${booking.trainer.displayName}
- Branch: ${booking.branch.name}
- Session: ${booking.course.usedSessions + 1} of ${booking.course.totalSessions}
- Duration: ${booking.durationMinutes} minutes
- Status: ${booking.status}
${booking.notes ? `- Notes: ${booking.notes}` : ''}
${booking.sessionNotes ? `- Session Notes: ${booking.sessionNotes}` : ''}
${booking.trainerRecommendations ? `- Recommendations: ${booking.trainerRecommendations}` : ''}

Booking ID: ${booking.id}
        `.trim(),
        start: {
          dateTime: startTime.toISOString(),
          timeZone: 'Asia/Bangkok',
        },
        end: {
          dateTime: endTime.toISOString(),
          timeZone: 'Asia/Bangkok',
        },
        location: booking.branch.address,
      };

      await this.calendar.events.update({
        calendarId: 'primary',
        eventId: eventId,
        requestBody: event,
      });

      this.logger.log(`Updated calendar event: ${eventId}`);
      return true;
    } catch (error) {
      this.logger.error('Failed to update calendar event', error);
      return false;
    }
  }

  async deleteBookingEvent(eventId: string): Promise<boolean> {
    if (!this.calendar || !eventId) {
      return false;
    }

    try {
      await this.calendar.events.delete({
        calendarId: 'primary',
        eventId: eventId,
      });

      this.logger.log(`Deleted calendar event: ${eventId}`);
      return true;
    } catch (error) {
      this.logger.error('Failed to delete calendar event', error);
      return false;
    }
  }

  async getAvailableSlots(
    date: string,
    startHour: number = 8,
    endHour: number = 20,
    slotDuration: number = 60,
  ): Promise<string[]> {
    if (!this.calendar) {
      return [];
    }

    try {
      const startOfDay = new Date(`${date}T${startHour.toString().padStart(2, '0')}:00:00+07:00`);
      const endOfDay = new Date(`${date}T${endHour.toString().padStart(2, '0')}:00:00+07:00`);

      const response = await this.calendar.events.list({
        calendarId: 'primary',
        timeMin: startOfDay.toISOString(),
        timeMax: endOfDay.toISOString(),
        singleEvents: true,
        orderBy: 'startTime',
      });

      const events = response.data.items || [];
      const availableSlots: string[] = [];

      // Generate all possible slots
      const currentSlot = new Date(startOfDay);
      while (currentSlot < endOfDay) {
        const slotEnd = new Date(currentSlot.getTime() + slotDuration * 60000);
        
        // Check if this slot conflicts with any existing event
        const hasConflict = events.some(event => {
          const eventStart = new Date(event.start?.dateTime || event.start?.date);
          const eventEnd = new Date(event.end?.dateTime || event.end?.date);
          
          return (currentSlot < eventEnd && slotEnd > eventStart);
        });

        if (!hasConflict) {
          availableSlots.push(currentSlot.toISOString());
        }

        currentSlot.setMinutes(currentSlot.getMinutes() + slotDuration);
      }

      return availableSlots;
    } catch (error) {
      this.logger.error('Failed to get available slots', error);
      return [];
    }
  }
}
