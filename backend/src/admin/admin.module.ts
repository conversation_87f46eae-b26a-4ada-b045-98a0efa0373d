import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdminCourseController } from './admin-course.controller';
import { AdminCourseService } from './admin-course.service';
import { AdminUserController } from './admin-user.controller';
import { AdminUserService } from './admin-user.service';
import { AdminBranchController } from './admin-branch.controller';
import { AdminBranchService } from './admin-branch.service';
import { UserBranchAccessController } from './user-branch-access.controller';
import { UserBranchAccessService } from './user-branch-access.service';
import { Course } from '../entities/course.entity';
import { User } from '../entities/user.entity';
import { Branch } from '../entities/branch.entity';
import { Booking } from '../entities/booking.entity';
import { ActivityLog } from '../entities/activity-log.entity';
import { UserBranchAccess } from '../entities/user-branch-access.entity';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Course,
      User,
      Branch,
      Booking,
      ActivityLog,
      UserBranchAccess,
    ]),
    AuthModule,
  ],
  controllers: [
    AdminCourseController,
    AdminUserController,
    AdminBranchController,
    UserBranchAccessController,
  ],
  providers: [
    AdminCourseService,
    AdminUserService,
    AdminBranchService,
    UserBranchAccessService,
  ],
  exports: [
    AdminCourseService,
    AdminUserService,
    AdminBranchService,
    UserBranchAccessService,
  ],
})
export class AdminModule {}
