import {
  Controller,
  Get,
  Put,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AdminRoles } from '../auth/decorators/admin-roles.decorator';
import { AdminRolesGuard } from '../auth/guards/admin-roles.guard';
import { GetAdmin } from '../auth/decorators/get-admin.decorator';
import { Admin, AdminRole } from '../entities/admin.entity';
import { AdminUserService } from './admin-user.service';
import {
  UserQueryDto,
  UpdateUserDto,
  BulkActivateUsersDto,
} from './dto/user.dto';

@Controller('admin/users')
@UseGuards(AuthGuard('admin-jwt'), AdminRolesGuard)
export class AdminUserController {
  constructor(private adminUserService: AdminUserService) {}

  @Get()
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async getAllUsers(@GetAdmin() admin: Admin, @Query() query: UserQueryDto) {
    return this.adminUserService.getAllUsers(admin, query);
  }

  @Get('inactive')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async getInactiveUsers(@GetAdmin() admin: Admin) {
    return this.adminUserService.getInactiveUsers(admin);
  }

  @Get(':id')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async getUserById(
    @Param('id', ParseIntPipe) id: number,
    @GetAdmin() admin: Admin,
  ) {
    return this.adminUserService.getUserById(id, admin);
  }

  @Put(':id/activate')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async activateUser(
    @Param('id', ParseIntPipe) id: number,
    @Body('reason') reason: string,
    @GetAdmin() admin: Admin,
  ) {
    return this.adminUserService.activateUser(id, admin, reason);
  }

  @Put(':id/deactivate')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async deactivateUser(
    @Param('id', ParseIntPipe) id: number,
    @Body('reason') reason: string,
    @GetAdmin() admin: Admin,
  ) {
    return this.adminUserService.deactivateUser(id, admin, reason);
  }

  @Put(':id')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async updateUserDetails(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateData: UpdateUserDto,
    @GetAdmin() admin: Admin,
  ) {
    return this.adminUserService.updateUserDetails(id, updateData, admin);
  }

  @Post('bulk-activate')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async bulkActivateUsers(
    @Body() bulkActivateDto: BulkActivateUsersDto,
    @GetAdmin() admin: Admin,
  ) {
    return this.adminUserService.bulkActivateUsers(
      bulkActivateDto.userIds,
      admin,
      bulkActivateDto.reason,
    );
  }
}
