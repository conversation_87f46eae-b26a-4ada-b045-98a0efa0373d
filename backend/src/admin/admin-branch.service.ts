import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, ILike } from 'typeorm';
import { Branch } from '../entities/branch.entity';
import { Admin, AdminRole } from '../entities/admin.entity';
import { User } from '../entities/user.entity';
import { Course } from '../entities/course.entity';
import { Booking } from '../entities/booking.entity';
import { ActivityLog, ActivityType } from '../entities/activity-log.entity';
import {
  CreateBranchDto,
  UpdateBranchDto,
  BranchQueryDto,
  BranchStatsDto,
} from './dto/branch.dto';

@Injectable()
export class AdminBranchService {
  constructor(
    @InjectRepository(Branch)
    private branchRepository: Repository<Branch>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Course)
    private courseRepository: Repository<Course>,
    @InjectRepository(Booking)
    private bookingRepository: Repository<Booking>,
    @InjectRepository(ActivityLog)
    private activityLogRepository: Repository<ActivityLog>,
  ) {}

  async getAllBranches(admin: Admin, query?: BranchQueryDto) {
    const {
      page = 1,
      limit = 10,
      search,
      isActive,
      sortBy = 'name',
      sortOrder = 'ASC',
    } = query || {};

    const queryBuilder = this.branchRepository.createQueryBuilder('branch');

    // Apply role-based filtering
    if (admin.role === AdminRole.BRANCH_ADMIN && admin.branch) {
      queryBuilder.where('branch.id = :branchId', {
        branchId: admin.branch.id,
      });
    } else if (admin.role !== AdminRole.SUPER_ADMIN) {
      return { branches: [], total: 0, page, limit };
    }

    // Apply filters
    if (search) {
      queryBuilder.andWhere(
        '(branch.name ILIKE :search OR branch.address ILIKE :search OR branch.email ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    if (isActive !== undefined) {
      queryBuilder.andWhere('branch.isActive = :isActive', { isActive });
    }

    // Apply sorting
    queryBuilder.orderBy(`branch.${sortBy}`, sortOrder);

    // Apply pagination
    const total = await queryBuilder.getCount();
    const branches = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    return {
      branches,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getBranchById(id: number, admin: Admin): Promise<Branch> {
    // Check permissions
    if (admin.role === AdminRole.BRANCH_ADMIN && admin.branch?.id !== id) {
      throw new ForbiddenException(
        'Branch admins can only access their own branch',
      );
    }

    const branch = await this.branchRepository.findOne({
      where: { id },
      relations: ['users', 'courses', 'bookings'],
    });

    if (!branch) {
      throw new NotFoundException('Branch not found');
    }

    return branch;
  }

  async createBranch(
    createBranchDto: CreateBranchDto,
    admin: Admin,
  ): Promise<Branch> {
    // Only super admins can create branches
    if (admin.role !== AdminRole.SUPER_ADMIN) {
      throw new ForbiddenException('Only super admins can create branches');
    }

    // Validate operating hours
    if (createBranchDto.openTime >= createBranchDto.closeTime) {
      throw new BadRequestException('Open time must be before close time');
    }

    // Check if branch name already exists
    const existingBranch = await this.branchRepository.findOne({
      where: { name: createBranchDto.name },
    });

    if (existingBranch) {
      throw new BadRequestException('Branch with this name already exists');
    }

    const branch = this.branchRepository.create(createBranchDto);
    const savedBranch = await this.branchRepository.save(branch);

    // Log activity
    await this.activityLogRepository.save({
      activityType: ActivityType.USER_REGISTRATION,
      description: `Branch "${savedBranch.name}" created`,
      admin,
      metadata: { branchId: savedBranch.id, branchName: savedBranch.name },
    });

    return savedBranch;
  }

  async updateBranch(
    id: number,
    updateBranchDto: UpdateBranchDto,
    admin: Admin,
  ): Promise<Branch> {
    // Check permissions
    if (admin.role === AdminRole.BRANCH_ADMIN && admin.branch?.id !== id) {
      throw new ForbiddenException(
        'Branch admins can only update their own branch',
      );
    }

    const branch = await this.branchRepository.findOne({ where: { id } });
    if (!branch) {
      throw new NotFoundException('Branch not found');
    }

    // Validate operating hours if provided
    const openTime = updateBranchDto.openTime || branch.openTime;
    const closeTime = updateBranchDto.closeTime || branch.closeTime;
    if (openTime >= closeTime) {
      throw new BadRequestException('Open time must be before close time');
    }

    // Check if new name conflicts with existing branch
    if (updateBranchDto.name && updateBranchDto.name !== branch.name) {
      const existingBranch = await this.branchRepository.findOne({
        where: { name: updateBranchDto.name },
      });
      if (existingBranch) {
        throw new BadRequestException('Branch with this name already exists');
      }
    }

    Object.assign(branch, updateBranchDto);
    const updatedBranch = await this.branchRepository.save(branch);

    // Log activity
    await this.activityLogRepository.save({
      activityType: ActivityType.USER_REGISTRATION,
      description: `Branch "${updatedBranch.name}" updated`,
      admin,
      metadata: { branchId: updatedBranch.id, changes: updateBranchDto },
    });

    return updatedBranch;
  }

  async deleteBranch(id: number, admin: Admin): Promise<void> {
    // Only super admins can delete branches
    if (admin.role !== AdminRole.SUPER_ADMIN) {
      throw new ForbiddenException('Only super admins can delete branches');
    }

    const branch = await this.branchRepository.findOne({
      where: { id },
      relations: ['users', 'courses', 'bookings'],
    });

    if (!branch) {
      throw new NotFoundException('Branch not found');
    }

    // Check if branch has active users, courses, or bookings
    const hasActiveUsers = branch.users?.some((user) => user.isActive);
    const hasActiveCourses = branch.courses?.some(
      (course) => course.status === 'active',
    );
    const hasActiveBookings = branch.bookings?.some((booking) =>
      ['scheduled', 'confirmed'].includes(booking.status),
    );

    if (hasActiveUsers || hasActiveCourses || hasActiveBookings) {
      throw new BadRequestException(
        'Cannot delete branch with active users, courses, or bookings. Please deactivate instead.',
      );
    }

    await this.branchRepository.remove(branch);

    // Log activity
    await this.activityLogRepository.save({
      activityType: ActivityType.USER_REGISTRATION,
      description: `Branch "${branch.name}" deleted`,
      admin,
      metadata: { branchId: id, branchName: branch.name },
    });
  }

  async getBranchStats(statsDto: BranchStatsDto, admin: Admin) {
    const { branchId, period = 'month', startDate, endDate } = statsDto;

    // Check permissions
    if (admin.role === AdminRole.BRANCH_ADMIN) {
      if (branchId && admin.branch?.id !== branchId) {
        throw new ForbiddenException(
          'Branch admins can only access their own branch stats',
        );
      }
      // Force branch admin to only see their branch
      statsDto.branchId = admin.branch?.id;
    }

    const whereCondition: any = {};
    if (statsDto.branchId) {
      whereCondition.branch = { id: statsDto.branchId };
    }

    // Date range filtering
    let dateFilter = {};
    if (startDate && endDate) {
      dateFilter = {
        createdAt: {
          $gte: new Date(startDate),
          $lte: new Date(endDate),
        },
      };
    } else {
      // Default period filtering
      const now = new Date();
      let fromDate: Date;

      switch (period) {
        case 'day':
          fromDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          fromDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          fromDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'year':
          fromDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          fromDate = new Date(now.getFullYear(), now.getMonth(), 1);
      }

      dateFilter = {
        createdAt: {
          $gte: fromDate,
          $lte: now,
        },
      };
    }

    // Get statistics
    const [
      totalUsers,
      activeUsers,
      totalCourses,
      activeCourses,
      totalBookings,
      completedBookings,
    ] = await Promise.all([
      this.userRepository.count({ where: { ...whereCondition } }),
      this.userRepository.count({
        where: { ...whereCondition, isActive: true },
      }),
      this.courseRepository.count({ where: { ...whereCondition } }),
      this.courseRepository.count({
        where: { ...whereCondition, status: 'active' },
      }),
      this.bookingRepository.count({ where: { ...whereCondition } }),
      this.bookingRepository.count({
        where: { ...whereCondition, status: 'completed' },
      }),
    ]);

    // Calculate revenue
    const revenueResult = await this.bookingRepository
      .createQueryBuilder('booking')
      .select('SUM(booking.price)', 'total')
      .where('booking.status = :status', { status: 'completed' })
      .andWhere(
        whereCondition.branch ? 'booking.branchId = :branchId' : '1=1',
        whereCondition.branch ? { branchId: whereCondition.branch.id } : {},
      )
      .getRawOne();

    const totalRevenue = parseFloat(revenueResult?.total || '0');

    return {
      totalUsers,
      activeUsers,
      totalCourses,
      activeCourses,
      totalBookings,
      completedBookings,
      totalRevenue,
      period,
      branchId: statsDto.branchId,
    };
  }

  async toggleBranchStatus(id: number, admin: Admin): Promise<Branch> {
    // Only super admins can toggle branch status
    if (admin.role !== AdminRole.SUPER_ADMIN) {
      throw new ForbiddenException(
        'Only super admins can toggle branch status',
      );
    }

    const branch = await this.branchRepository.findOne({ where: { id } });
    if (!branch) {
      throw new NotFoundException('Branch not found');
    }

    branch.isActive = !branch.isActive;
    const updatedBranch = await this.branchRepository.save(branch);

    // Log activity
    await this.activityLogRepository.save({
      activityType: ActivityType.USER_REGISTRATION,
      description: `Branch "${branch.name}" ${branch.isActive ? 'activated' : 'deactivated'}`,
      admin,
      metadata: { branchId: id, isActive: branch.isActive },
    });

    return updatedBranch;
  }
}
