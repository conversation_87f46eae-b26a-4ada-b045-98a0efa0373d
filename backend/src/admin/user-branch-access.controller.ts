import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AdminRoles } from '../auth/decorators/admin-roles.decorator';
import { AdminRolesGuard } from '../auth/guards/admin-roles.guard';
import { GetAdmin } from '../auth/decorators/get-admin.decorator';
import { Admin, AdminRole } from '../entities/admin.entity';
import { UserBranchAccessService } from './user-branch-access.service';
import {
  GrantBranchAccessDto,
  GrantMultipleBranchAccessDto,
  UpdateBranchAccessDto,
  RevokeBranchAccessDto,
  BulkRevokeBranchAccessDto,
  BranchAccessQueryDto,
} from './dto/user-branch-access.dto';

@Controller('admin/user-branch-access')
@UseGuards(AuthGuard('admin-jwt'), AdminRolesGuard)
export class UserBranchAccessController {
  constructor(
    private readonly userBranchAccessService: UserBranchAccessService,
  ) {}

  @Post('grant')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async grantBranchAccess(
    @Body() grantBranchAccessDto: GrantBranchAccessDto,
    @GetAdmin() admin: Admin,
  ) {
    const access = await this.userBranchAccessService.grantBranchAccess(
      grantBranchAccessDto,
      admin,
    );
    return {
      success: true,
      message: 'Branch access granted successfully',
      data: access,
    };
  }

  @Post('grant-multiple')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async grantMultipleBranchAccess(
    @Body() grantMultipleBranchAccessDto: GrantMultipleBranchAccessDto,
    @GetAdmin() admin: Admin,
  ) {
    const result = await this.userBranchAccessService.grantMultipleBranchAccess(
      grantMultipleBranchAccessDto,
      admin,
    );
    return {
      success: true,
      message: `Branch access granted: ${result.totalGranted} successful, ${result.totalErrors} failed`,
      data: result,
    };
  }

  @Put(':accessId')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async updateBranchAccess(
    @Param('accessId', ParseIntPipe) accessId: number,
    @Body() updateBranchAccessDto: UpdateBranchAccessDto,
    @GetAdmin() admin: Admin,
  ) {
    const access = await this.userBranchAccessService.updateBranchAccess(
      accessId,
      updateBranchAccessDto,
      admin,
    );
    return {
      success: true,
      message: 'Branch access updated successfully',
      data: access,
    };
  }

  @Post('revoke')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async revokeBranchAccess(
    @Body() revokeBranchAccessDto: RevokeBranchAccessDto,
    @GetAdmin() admin: Admin,
  ) {
    const access = await this.userBranchAccessService.revokeBranchAccess(
      revokeBranchAccessDto,
      admin,
    );
    return {
      success: true,
      message: 'Branch access revoked successfully',
      data: access,
    };
  }

  @Post('bulk-revoke')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async bulkRevokeBranchAccess(
    @Body() bulkRevokeBranchAccessDto: BulkRevokeBranchAccessDto,
    @GetAdmin() admin: Admin,
  ) {
    const result = await this.userBranchAccessService.bulkRevokeBranchAccess(
      bulkRevokeBranchAccessDto,
      admin,
    );
    return {
      success: true,
      message: `Branch access revoked: ${result.totalRevoked} successful, ${result.totalErrors} failed`,
      data: result,
    };
  }

  @Get('user/:userId')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async getUserBranchAccess(
    @Param('userId', ParseIntPipe) userId: number,
    @GetAdmin() admin: Admin,
  ) {
    const access = await this.userBranchAccessService.getUserBranchAccess(
      userId,
      admin,
    );
    return {
      success: true,
      data: access,
    };
  }

  @Get('branch/:branchId')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async getBranchUserAccess(
    @Param('branchId', ParseIntPipe) branchId: number,
    @GetAdmin() admin: Admin,
  ) {
    const access = await this.userBranchAccessService.getBranchUserAccess(
      branchId,
      admin,
    );
    return {
      success: true,
      data: access,
    };
  }

  @Get()
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async getAllBranchAccess(
    @Query() query: BranchAccessQueryDto,
    @GetAdmin() admin: Admin,
  ) {
    const result = await this.userBranchAccessService.getAllBranchAccess(
      query,
      admin,
    );
    return {
      success: true,
      data: result,
    };
  }

  @Get('customer/:userId/eligible-branches')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async getCustomerEligibleBranches(
    @Param('userId', ParseIntPipe) userId: number,
    @GetAdmin() admin: Admin,
  ) {
    const branches =
      await this.userBranchAccessService.getCustomerEligibleBranches(userId);
    return {
      success: true,
      data: branches,
    };
  }

  @Get('check/:userId/:branchId')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async checkCustomerBranchAccess(
    @Param('userId', ParseIntPipe) userId: number,
    @Param('branchId', ParseIntPipe) branchId: number,
    @GetAdmin() admin: Admin,
  ) {
    const hasAccess =
      await this.userBranchAccessService.hasCustomerBranchAccess(
        userId,
        branchId,
      );
    return {
      success: true,
      data: { hasAccess },
    };
  }
}
