import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  UserBranchAccess,
  AccessLevel,
} from '../entities/user-branch-access.entity';
import { User, UserRole } from '../entities/user.entity';
import { Branch } from '../entities/branch.entity';
import { Admin, AdminRole } from '../entities/admin.entity';
import { ActivityLog, ActivityType } from '../entities/activity-log.entity';
import {
  GrantBranchAccessDto,
  GrantMultipleBranchAccessDto,
  UpdateBranchAccessDto,
  RevokeBranchAccessDto,
  BulkRevokeBranchAccessDto,
  BranchAccessQueryDto,
} from './dto/user-branch-access.dto';

/**
 * Service for managing customer branch access (eligible branches for reservations)
 * Allows admins to control which branches customers can make reservations at
 */
@Injectable()
export class UserBranchAccessService {
  constructor(
    @InjectRepository(UserBranchAccess)
    private userBranchAccessRepository: Repository<UserBranchAccess>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Branch)
    private branchRepository: Repository<Branch>,
    @InjectRepository(ActivityLog)
    private activityLogRepository: Repository<ActivityLog>,
  ) {}

  async grantBranchAccess(
    grantBranchAccessDto: GrantBranchAccessDto,
    admin: Admin,
  ) {
    const { userId, branchId, accessLevel, notes } = grantBranchAccessDto;

    // Verify user exists
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Verify branch exists
    const branch = await this.branchRepository.findOne({
      where: { id: branchId },
    });
    if (!branch) {
      throw new NotFoundException('Branch not found');
    }

    // Check if admin has permission to grant access to this branch
    if (
      admin.role === AdminRole.BRANCH_ADMIN &&
      admin.branch?.id !== branchId
    ) {
      throw new ForbiddenException(
        'Branch admins can only grant access to their own branch',
      );
    }

    // Check if access already exists
    const existingAccess = await this.userBranchAccessRepository.findOne({
      where: { user: { id: userId }, branch: { id: branchId } },
    });

    if (existingAccess) {
      if (existingAccess.isActive) {
        throw new BadRequestException(
          'User already has active access to this branch',
        );
      }
      // Reactivate existing access
      existingAccess.isActive = true;
      existingAccess.accessLevel = accessLevel;
      existingAccess.notes = notes;
      existingAccess.grantedAt = new Date();
      existingAccess.grantedBy = admin;
      existingAccess.revokedAt = undefined;
      existingAccess.revokedBy = undefined;

      const savedAccess =
        await this.userBranchAccessRepository.save(existingAccess);

      // Log activity
      await this.activityLogRepository.save({
        activityType: ActivityType.USER_REGISTRATION,
        description: `Reservation access reactivated for customer ${user.displayName} to branch ${branch.name}`,
        admin,
        metadata: { userId, branchId, accessLevel, notes },
      });

      return savedAccess;
    }

    // Create new access
    const branchAccess = this.userBranchAccessRepository.create({
      user,
      branch,
      accessLevel,
      notes,
      isActive: true,
      grantedAt: new Date(),
      grantedBy: admin,
    });

    const savedAccess =
      await this.userBranchAccessRepository.save(branchAccess);

    // Log activity
    await this.activityLogRepository.save({
      activityType: ActivityType.USER_REGISTRATION,
      description: `Reservation access granted to customer ${user.displayName} for branch ${branch.name}`,
      admin,
      metadata: { userId, branchId, accessLevel, notes },
    });

    return savedAccess;
  }

  async grantMultipleBranchAccess(
    grantMultipleBranchAccessDto: GrantMultipleBranchAccessDto,
    admin: Admin,
  ) {
    const { userId, branchAccess } = grantMultipleBranchAccessDto;

    // Verify user exists
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const results: UserBranchAccess[] = [];
    const errors: { branchId: number; error: string }[] = [];

    for (const access of branchAccess) {
      try {
        const result = await this.grantBranchAccess(
          {
            userId,
            branchId: access.branchId,
            accessLevel: access.accessLevel,
            notes: access.notes,
          },
          admin,
        );
        results.push(result);
      } catch (error) {
        errors.push({
          branchId: access.branchId,
          error: error.message,
        });
      }
    }

    return {
      success: results,
      errors,
      totalGranted: results.length,
      totalErrors: errors.length,
    };
  }

  async updateBranchAccess(
    accessId: number,
    updateBranchAccessDto: UpdateBranchAccessDto,
    admin: Admin,
  ) {
    const access = await this.userBranchAccessRepository.findOne({
      where: { id: accessId },
      relations: ['user', 'branch'],
    });

    if (!access) {
      throw new NotFoundException('Branch access not found');
    }

    // Check if admin has permission to update this access
    if (
      admin.role === AdminRole.BRANCH_ADMIN &&
      admin.branch?.id !== access.branch.id
    ) {
      throw new ForbiddenException(
        'Branch admins can only update access for their own branch',
      );
    }

    Object.assign(access, updateBranchAccessDto);
    const savedAccess = await this.userBranchAccessRepository.save(access);

    // Log activity
    await this.activityLogRepository.save({
      activityType: ActivityType.COURSE_MODIFIED,
      description: `Branch access updated for user ${access.user.displayName} to branch ${access.branch.name}`,
      admin,
      metadata: { accessId, updates: updateBranchAccessDto },
    });

    return savedAccess;
  }

  async revokeBranchAccess(
    revokeBranchAccessDto: RevokeBranchAccessDto,
    admin: Admin,
  ) {
    const { userId, branchId, reason } = revokeBranchAccessDto;

    const access = await this.userBranchAccessRepository.findOne({
      where: { user: { id: userId }, branch: { id: branchId } },
      relations: ['user', 'branch'],
    });

    if (!access) {
      throw new NotFoundException('Branch access not found');
    }

    // Check if admin has permission to revoke this access
    if (
      admin.role === AdminRole.BRANCH_ADMIN &&
      admin.branch?.id !== branchId
    ) {
      throw new ForbiddenException(
        'Branch admins can only revoke access for their own branch',
      );
    }

    access.isActive = false;
    access.revokedAt = new Date();
    access.revokedBy = admin;
    if (reason) {
      access.notes = access.notes
        ? `${access.notes}\nRevoked: ${reason}`
        : `Revoked: ${reason}`;
    }

    const savedAccess = await this.userBranchAccessRepository.save(access);

    // Log activity
    await this.activityLogRepository.save({
      activityType: ActivityType.COURSE_MODIFIED,
      description: `Branch access revoked for user ${access.user.displayName} from branch ${access.branch.name}`,
      admin,
      metadata: { userId, branchId, reason },
    });

    return savedAccess;
  }

  async bulkRevokeBranchAccess(
    bulkRevokeBranchAccessDto: BulkRevokeBranchAccessDto,
    admin: Admin,
  ) {
    const { userId, branchIds, reason } = bulkRevokeBranchAccessDto;

    const results: UserBranchAccess[] = [];
    const errors: { branchId: number; error: string }[] = [];

    for (const branchId of branchIds) {
      try {
        const result = await this.revokeBranchAccess(
          { userId, branchId, reason },
          admin,
        );
        results.push(result);
      } catch (error) {
        errors.push({
          branchId,
          error: error.message,
        });
      }
    }

    return {
      success: results,
      errors,
      totalRevoked: results.length,
      totalErrors: errors.length,
    };
  }

  async getUserBranchAccess(userId: number, admin: Admin) {
    // Check if admin has permission to view this user's access
    const whereCondition: any = { user: { id: userId } };

    if (admin.role === AdminRole.BRANCH_ADMIN) {
      whereCondition.branch = { id: admin.branch?.id };
    }

    const access = await this.userBranchAccessRepository.find({
      where: whereCondition,
      relations: ['user', 'branch', 'grantedBy', 'revokedBy'],
      order: { createdAt: 'DESC' },
    });

    return access;
  }

  async getBranchUserAccess(branchId: number, admin: Admin) {
    // Check if admin has permission to view this branch's access
    if (
      admin.role === AdminRole.BRANCH_ADMIN &&
      admin.branch?.id !== branchId
    ) {
      throw new ForbiddenException(
        'Branch admins can only view access for their own branch',
      );
    }

    const access = await this.userBranchAccessRepository.find({
      where: { branch: { id: branchId } },
      relations: ['user', 'branch', 'grantedBy', 'revokedBy'],
      order: { createdAt: 'DESC' },
    });

    return access;
  }

  async getAllBranchAccess(query: BranchAccessQueryDto, admin: Admin) {
    const {
      userId,
      branchId,
      accessLevel,
      isActive,
      page = 1,
      limit = 10,
    } = query;

    const whereCondition: any = {};

    if (userId) whereCondition.user = { id: userId };
    if (branchId) whereCondition.branch = { id: branchId };
    if (accessLevel) whereCondition.accessLevel = accessLevel;
    if (isActive !== undefined) whereCondition.isActive = isActive;

    // Branch admins can only see access for their branch
    if (admin.role === AdminRole.BRANCH_ADMIN) {
      whereCondition.branch = { id: admin.branch?.id };
    }

    const [access, total] = await this.userBranchAccessRepository.findAndCount({
      where: whereCondition,
      relations: ['user', 'branch', 'grantedBy', 'revokedBy'],
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      access,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Check if a customer has access to make reservations at a specific branch
   * This method can be used by the booking system to validate reservations
   */
  async hasCustomerBranchAccess(
    userId: number,
    branchId: number,
  ): Promise<boolean> {
    const access = await this.userBranchAccessRepository.findOne({
      where: {
        user: { id: userId },
        branch: { id: branchId },
        isActive: true,
      },
    });

    return !!access;
  }

  /**
   * Get all eligible branches for a customer (for reservation purposes)
   */
  async getCustomerEligibleBranches(userId: number): Promise<Branch[]> {
    const accessRecords = await this.userBranchAccessRepository.find({
      where: {
        user: { id: userId },
        isActive: true,
      },
      relations: ['branch'],
    });

    return accessRecords.map((access) => access.branch);
  }
}
