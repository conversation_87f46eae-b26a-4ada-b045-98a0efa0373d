import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AdminRoles } from '../auth/decorators/admin-roles.decorator';
import { AdminRolesGuard } from '../auth/guards/admin-roles.guard';
import { GetAdmin } from '../auth/decorators/get-admin.decorator';
import { Admin, AdminRole } from '../entities/admin.entity';
import { AdminBranchService } from './admin-branch.service';
import {
  CreateBranchDto,
  UpdateBranchDto,
  BranchQueryDto,
  BranchStatsDto,
} from './dto/branch.dto';

@Controller('admin/branches')
@UseGuards(AuthGuard('admin-jwt'), AdminRolesGuard)
export class AdminBranchController {
  constructor(private adminBranchService: AdminBranchService) {}

  @Get()
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async getAllBranches(
    @GetAdmin() admin: Admin,
    @Query() query: BranchQueryDto,
  ) {
    const result = await this.adminBranchService.getAllBranches(admin, query);
    return {
      success: true,
      data: result,
    };
  }

  @Get('stats')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async getBranchStats(
    @GetAdmin() admin: Admin,
    @Query() statsDto: BranchStatsDto,
  ) {
    const stats = await this.adminBranchService.getBranchStats(statsDto, admin);
    return {
      success: true,
      data: stats,
    };
  }

  @Get(':id')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async getBranchById(
    @Param('id', ParseIntPipe) id: number,
    @GetAdmin() admin: Admin,
  ) {
    const branch = await this.adminBranchService.getBranchById(id, admin);
    return {
      success: true,
      data: branch,
    };
  }

  @Post()
  @AdminRoles(AdminRole.SUPER_ADMIN)
  async createBranch(
    @Body() createBranchDto: CreateBranchDto,
    @GetAdmin() admin: Admin,
  ) {
    const branch = await this.adminBranchService.createBranch(
      createBranchDto,
      admin,
    );
    return {
      success: true,
      message: 'Branch created successfully',
      data: branch,
    };
  }

  @Put(':id')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async updateBranch(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateBranchDto: UpdateBranchDto,
    @GetAdmin() admin: Admin,
  ) {
    const branch = await this.adminBranchService.updateBranch(
      id,
      updateBranchDto,
      admin,
    );
    return {
      success: true,
      message: 'Branch updated successfully',
      data: branch,
    };
  }

  @Put(':id/toggle-status')
  @AdminRoles(AdminRole.SUPER_ADMIN)
  async toggleBranchStatus(
    @Param('id', ParseIntPipe) id: number,
    @GetAdmin() admin: Admin,
  ) {
    const branch = await this.adminBranchService.toggleBranchStatus(id, admin);
    return {
      success: true,
      message: `Branch ${branch.isActive ? 'activated' : 'deactivated'} successfully`,
      data: branch,
    };
  }

  @Delete(':id')
  @AdminRoles(AdminRole.SUPER_ADMIN)
  async deleteBranch(
    @Param('id', ParseIntPipe) id: number,
    @GetAdmin() admin: Admin,
  ) {
    await this.adminBranchService.deleteBranch(id, admin);
    return {
      success: true,
      message: 'Branch deleted successfully',
    };
  }
}
