import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Course, CourseStatus } from '../entities/course.entity';
import { User } from '../entities/user.entity';
import { Booking } from '../entities/booking.entity';
import { ActivityLog, ActivityType } from '../entities/activity-log.entity';
import { Admin, AdminRole } from '../entities/admin.entity';
import {
  CreateCourseDto,
  UpdateCourseDto,
  TransferSessionsDto,
} from './dto/course.dto';

@Injectable()
export class AdminCourseService {
  constructor(
    @InjectRepository(Course)
    private courseRepository: Repository<Course>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Booking)
    private bookingRepository: Repository<Booking>,
    @InjectRepository(ActivityLog)
    private activityLogRepository: Repository<ActivityLog>,
  ) {}

  async getAllCourses(admin: Admin, query: any) {
    const { page = 1, limit = 10, search, status, customerId } = query;
    const skip = (page - 1) * limit;

    const queryBuilder = this.courseRepository
      .createQueryBuilder('course')
      .leftJoinAndSelect('course.customer', 'customer')
      .leftJoinAndSelect('course.branch', 'branch')
      .leftJoinAndSelect('course.bookings', 'bookings');

    // Branch admin can only see courses from their branch
    if (admin.role === AdminRole.BRANCH_ADMIN && admin.branch) {
      queryBuilder.where('course.branchId = :branchId', {
        branchId: admin.branch.id,
      });
    }

    if (search) {
      queryBuilder.andWhere(
        '(course.name LIKE :search OR customer.displayName LIKE :search)',
        { search: `%${search}%` },
      );
    }

    if (status) {
      queryBuilder.andWhere('course.status = :status', { status });
    }

    if (customerId) {
      queryBuilder.andWhere('course.customerId = :customerId', { customerId });
    }

    queryBuilder.orderBy('course.createdAt', 'DESC').skip(skip).take(limit);

    const [courses, total] = await queryBuilder.getManyAndCount();

    return {
      success: true,
      data: {
        courses,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    };
  }

  async getCourseById(id: number, admin: Admin) {
    const course = await this.courseRepository.findOne({
      where: { id },
      relations: ['customer', 'branch', 'bookings', 'bookings.trainer'],
    });

    if (!course) {
      throw new NotFoundException('Course not found');
    }

    // Branch admin can only access courses from their branch
    if (admin.role === AdminRole.BRANCH_ADMIN && admin.branch) {
      if (course.branch?.id !== admin.branch.id) {
        throw new ForbiddenException('Access denied to this course');
      }
    }

    return {
      success: true,
      data: course,
    };
  }

  async getCourseSessions(id: number, admin: Admin) {
    const course = await this.getCourseById(id, admin);

    const sessions = await this.bookingRepository.find({
      where: { course: { id } },
      relations: ['trainer', 'customer'],
      order: { scheduledDateTime: 'DESC' },
    });

    return {
      success: true,
      data: {
        course: course.data,
        sessions,
        summary: {
          totalSessions: course.data.totalSessions,
          usedSessions: course.data.usedSessions,
          remainingSessions: course.data.remainingSessions,
          completedSessions: sessions.filter((s) => s.status === 'completed')
            .length,
          scheduledSessions: sessions.filter((s) => s.status === 'scheduled')
            .length,
          cancelledSessions: sessions.filter((s) => s.status === 'cancelled')
            .length,
        },
      },
    };
  }

  async createCourse(createCourseDto: CreateCourseDto, admin: Admin) {
    const { customerId, branchId, ...courseData } = createCourseDto;

    // Verify customer exists
    const customer = await this.userRepository.findOne({
      where: { id: customerId },
    });

    if (!customer) {
      throw new NotFoundException('Customer not found');
    }

    // Use admin's branch if not specified
    const finalBranchId = branchId || admin.branch?.id;

    if (!finalBranchId) {
      throw new BadRequestException('Branch is required');
    }

    // Branch admin can only create courses for their branch
    if (admin.role === AdminRole.BRANCH_ADMIN && admin.branch) {
      if (finalBranchId !== admin.branch.id) {
        throw new ForbiddenException('Can only create courses for your branch');
      }
    }

    const course = this.courseRepository.create({
      ...courseData,
      customer: { id: customerId },
      branch: { id: finalBranchId },
      remainingSessions: courseData.totalSessions,
      usedSessions: 0,
      status: CourseStatus.ACTIVE,
    });

    const savedCourse = await this.courseRepository.save(course);

    // Update customer total spent
    customer.totalSpent =
      Number(customer.totalSpent) + Number(courseData.price);
    await this.userRepository.save(customer);

    // Log activity
    await this.logActivity(
      ActivityType.COURSE_PURCHASE,
      `Course created by admin: ${courseData.name} for ${customer.displayName}`,
      customer,
      admin,
      {
        courseId: savedCourse.id,
        price: courseData.price,
        totalSessions: courseData.totalSessions,
      },
    );

    const result = await this.courseRepository.findOne({
      where: { id: savedCourse.id },
      relations: ['customer', 'branch'],
    });

    return {
      success: true,
      data: result,
      message: 'Course created successfully',
    };
  }

  async updateCourse(
    id: number,
    updateCourseDto: UpdateCourseDto,
    admin: Admin,
  ) {
    const course = await this.getCourseById(id, admin);

    Object.assign(course.data, updateCourseDto);

    const updatedCourse = await this.courseRepository.save(course.data);

    // Log activity
    await this.logActivity(
      ActivityType.COURSE_PURCHASE, // Using closest match
      `Course updated by admin: ${updatedCourse.name}`,
      course.data.customer,
      admin,
      { courseId: id, changes: updateCourseDto },
    );

    return {
      success: true,
      data: updatedCourse,
      message: 'Course updated successfully',
    };
  }

  async transferSessions(
    fromCourseId: number,
    transferDto: TransferSessionsDto,
    admin: Admin,
  ) {
    const { toCourseId, sessionCount, reason } = transferDto;

    const fromCourse = await this.getCourseById(fromCourseId, admin);
    const toCourse = await this.getCourseById(toCourseId, admin);

    if (fromCourse.data.remainingSessions < sessionCount) {
      throw new BadRequestException('Not enough sessions to transfer');
    }

    // Update session counts
    fromCourse.data.remainingSessions -= sessionCount;
    toCourse.data.remainingSessions += sessionCount;
    toCourse.data.totalSessions += sessionCount;

    await this.courseRepository.save(fromCourse.data);
    await this.courseRepository.save(toCourse.data);

    // Log activity
    await this.logActivity(
      ActivityType.COURSE_PURCHASE, // Using closest match
      `Sessions transferred: ${sessionCount} from ${fromCourse.data.name} to ${toCourse.data.name}. Reason: ${reason}`,
      fromCourse.data.customer,
      admin,
      { fromCourseId, toCourseId, sessionCount, reason },
    );

    return {
      success: true,
      message: `${sessionCount} sessions transferred successfully`,
      data: {
        fromCourse: fromCourse.data,
        toCourse: toCourse.data,
      },
    };
  }

  async extendExpiry(id: number, newExpiryDate: Date, admin: Admin) {
    const course = await this.getCourseById(id, admin);

    if (newExpiryDate <= course.data.expiryDate) {
      throw new BadRequestException(
        'New expiry date must be later than current expiry date',
      );
    }

    course.data.expiryDate = newExpiryDate;
    const updatedCourse = await this.courseRepository.save(course.data);

    // Log activity
    await this.logActivity(
      ActivityType.COURSE_PURCHASE, // Using closest match
      `Course expiry extended: ${course.data.name} extended to ${newExpiryDate.toDateString()}`,
      course.data.customer,
      admin,
      { courseId: id, newExpiryDate },
    );

    return {
      success: true,
      data: updatedCourse,
      message: 'Course expiry extended successfully',
    };
  }

  async addSessions(
    id: number,
    additionalSessions: number,
    reason: string,
    admin: Admin,
  ) {
    const course = await this.getCourseById(id, admin);

    course.data.totalSessions += additionalSessions;
    course.data.remainingSessions += additionalSessions;

    const updatedCourse = await this.courseRepository.save(course.data);

    // Log activity
    await this.logActivity(
      ActivityType.COURSE_PURCHASE, // Using closest match
      `Sessions added: ${additionalSessions} sessions added to ${course.data.name}. Reason: ${reason}`,
      course.data.customer,
      admin,
      { courseId: id, additionalSessions, reason },
    );

    return {
      success: true,
      data: updatedCourse,
      message: `${additionalSessions} sessions added successfully`,
    };
  }

  async deleteCourse(id: number, admin: Admin) {
    const course = await this.getCourseById(id, admin);

    // Check if course has any bookings
    const bookingCount = await this.bookingRepository.count({
      where: { course: { id } },
    });

    if (bookingCount > 0) {
      throw new BadRequestException(
        'Cannot delete course with existing bookings',
      );
    }

    await this.courseRepository.remove(course.data);

    // Log activity
    await this.logActivity(
      ActivityType.COURSE_PURCHASE, // Using closest match
      `Course deleted: ${course.data.name}`,
      course.data.customer,
      admin,
      { courseId: id },
    );

    return {
      success: true,
      message: 'Course deleted successfully',
    };
  }

  private async logActivity(
    activityType: ActivityType,
    description: string,
    user?: User,
    admin?: Admin,
    metadata?: any,
  ): Promise<void> {
    const log = this.activityLogRepository.create({
      activityType,
      description,
      user,
      admin,
      metadata,
    });
    await this.activityLogRepository.save(log);
  }
}
