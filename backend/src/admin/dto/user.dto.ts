import {
  IsString,
  <PERSON><PERSON><PERSON>ber,
  IsO<PERSON>al,
  IsEnum,
  IsEmail,
  IsNotEmpty,
  IsArray,
  ArrayMinSize,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { UserRole, MembershipLevel } from '../../entities/user.entity';

export class UserQueryDto {
  @IsOptional()
  @Transform(({ value }) => (value ? parseInt(value, 10) : 1))
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => (value ? parseInt(value, 10) : 10))
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  status?: string; // 'active' or 'inactive'

  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;
}

export class UpdateUserDto {
  @IsOptional()
  @IsEmail()
  @IsNotEmpty()
  email?: string;

  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @IsOptional()
  @IsEnum(MembershipLevel)
  membershipLevel?: MembershipLevel;
}

export class BulkActivateUsersDto {
  @IsArray()
  @ArrayMinSize(1)
  @IsNumber({}, { each: true })
  userIds: number[];

  @IsOptional()
  @IsString()
  reason?: string;
}
