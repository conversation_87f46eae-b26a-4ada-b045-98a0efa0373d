import {
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  IsOptional,
  IsDateString,
  IsEnum,
  IsPositive,
  Min,
  Max,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { CourseStatus } from '../../entities/course.entity';

export class CreateCourseDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNumber()
  @IsPositive()
  price: number;

  @IsNumber()
  @IsPositive()
  @Min(1)
  @Max(100)
  totalSessions: number;

  @IsDateString()
  startDate: string;

  @IsDateString()
  expiryDate: string;

  @IsNumber()
  @IsPositive()
  customerId: number;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  branchId?: number;
}

export class UpdateCourseDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  price?: number;

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  expiryDate?: string;

  @IsOptional()
  @IsEnum(CourseStatus)
  status?: CourseStatus;
}

export class TransferSessionsDto {
  @IsNumber()
  @IsPositive()
  toCourseId: number;

  @IsNumber()
  @IsPositive()
  @Min(1)
  sessionCount: number;

  @IsString()
  reason: string;
}

export class CourseQueryDto {
  @IsOptional()
  @Transform(({ value }) => (value ? parseInt(value, 10) : 1))
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => (value ? parseInt(value, 10) : 10))
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsEnum(CourseStatus)
  status?: CourseStatus;

  @IsOptional()
  @Transform(({ value }) => (value ? parseInt(value, 10) : undefined))
  @IsNumber()
  @IsPositive()
  customerId?: number;
}
