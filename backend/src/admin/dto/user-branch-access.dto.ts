import {
  IsString,
  <PERSON><PERSON><PERSON>ber,
  IsOptional,
  IsEnum,
  IsBoolean,
  IsPositive,
  IsArray,
  ArrayMinSize,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { AccessLevel } from '../../entities/user-branch-access.entity';

export class GrantBranchAccessDto {
  @IsNumber()
  @IsPositive()
  userId: number;

  @IsNumber()
  @IsPositive()
  branchId: number;

  @IsEnum(AccessLevel)
  accessLevel: AccessLevel;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class GrantMultipleBranchAccessDto {
  @IsNumber()
  @IsPositive()
  userId: number;

  @IsArray()
  @ArrayMinSize(1)
  branchAccess: {
    branchId: number;
    accessLevel: AccessLevel;
    notes?: string;
  }[];
}

export class UpdateBranchAccessDto {
  @IsOptional()
  @IsEnum(AccessLevel)
  accessLevel?: AccessLevel;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class RevokeBranchAccessDto {
  @IsNumber()
  @IsPositive()
  userId: number;

  @IsNumber()
  @IsPositive()
  branchId: number;

  @IsOptional()
  @IsString()
  reason?: string;
}

export class BulkRevokeBranchAccessDto {
  @IsNumber()
  @IsPositive()
  userId: number;

  @IsArray()
  @ArrayMinSize(1)
  branchIds: number[];

  @IsOptional()
  @IsString()
  reason?: string;
}

export class BranchAccessQueryDto {
  @IsOptional()
  @Transform(({ value }) => (value ? parseInt(value, 10) : undefined))
  @IsNumber()
  @IsPositive()
  userId?: number;

  @IsOptional()
  @Transform(({ value }) => (value ? parseInt(value, 10) : undefined))
  @IsNumber()
  @IsPositive()
  branchId?: number;

  @IsOptional()
  @IsEnum(AccessLevel)
  accessLevel?: AccessLevel;

  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @Transform(({ value }) => (value ? parseInt(value, 10) : 1))
  @IsNumber()
  @IsPositive()
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => (value ? parseInt(value, 10) : 10))
  @IsNumber()
  @IsPositive()
  limit?: number = 10;
}
