import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserRole } from '../entities/user.entity';
import { ActivityLog, ActivityType } from '../entities/activity-log.entity';
import { Admin, AdminRole } from '../entities/admin.entity';
import { UpdateUserDto } from './dto/user.dto';

@Injectable()
export class AdminUserService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(ActivityLog)
    private activityLogRepository: Repository<ActivityLog>,
  ) {}

  async getAllUsers(admin: Admin, query: any) {
    const { page = 1, limit = 10, search, status, role } = query;
    const skip = (page - 1) * limit;

    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.branch', 'branch')
      .leftJoinAndSelect('user.courses', 'courses');

    // Branch admin can only see users from their branch
    if (admin.role === AdminRole.BRANCH_ADMIN && admin.branch) {
      queryBuilder.where('user.branchId = :branchId', {
        branchId: admin.branch.id,
      });
    }

    if (search) {
      queryBuilder.andWhere(
        '(user.displayName LIKE :search OR user.email LIKE :search OR user.phoneNumber LIKE :search)',
        { search: `%${search}%` },
      );
    }

    if (status) {
      const isActive = status === 'active';
      queryBuilder.andWhere('user.isActive = :isActive', { isActive });
    }

    if (role) {
      queryBuilder.andWhere('user.role = :role', { role });
    }

    queryBuilder.orderBy('user.createdAt', 'DESC').skip(skip).take(limit);

    const [users, total] = await queryBuilder.getManyAndCount();

    return {
      success: true,
      data: {
        users,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    };
  }

  async getUserById(id: number, admin: Admin) {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['branch', 'courses', 'bookings'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Branch admin can only access users from their branch
    if (admin.role === AdminRole.BRANCH_ADMIN && admin.branch) {
      if (user.branch?.id !== admin.branch.id) {
        throw new ForbiddenException('Access denied to this user');
      }
    }

    return {
      success: true,
      data: user,
    };
  }

  async activateUser(id: number, admin: Admin, reason?: string) {
    const user = await this.getUserById(id, admin);

    if (user.data.isActive) {
      throw new BadRequestException('User is already active');
    }

    user.data.isActive = true;
    const updatedUser = await this.userRepository.save(user.data);

    // Log activity
    await this.logActivity(
      ActivityType.USER_REGISTRATION, // Using closest match
      `User activated by admin: ${updatedUser.displayName}${reason ? `. Reason: ${reason}` : ''}`,
      updatedUser,
      admin,
      { reason, previousStatus: 'inactive' },
    );

    return {
      success: true,
      data: updatedUser,
      message: 'User activated successfully',
    };
  }

  async deactivateUser(id: number, admin: Admin, reason?: string) {
    const user = await this.getUserById(id, admin);

    if (!user.data.isActive) {
      throw new BadRequestException('User is already inactive');
    }

    user.data.isActive = false;
    const updatedUser = await this.userRepository.save(user.data);

    // Log activity
    await this.logActivity(
      ActivityType.USER_REGISTRATION, // Using closest match
      `User deactivated by admin: ${updatedUser.displayName}${reason ? `. Reason: ${reason}` : ''}`,
      updatedUser,
      admin,
      { reason, previousStatus: 'active' },
    );

    return {
      success: true,
      data: updatedUser,
      message: 'User deactivated successfully',
    };
  }

  async updateUserDetails(id: number, updateData: UpdateUserDto, admin: Admin) {
    const user = await this.getUserById(id, admin);

    // Update allowed fields directly from the DTO
    if (updateData.email !== undefined) {
      user.data.email = updateData.email;
    }
    if (updateData.phoneNumber !== undefined) {
      user.data.phoneNumber = updateData.phoneNumber;
    }
    if (updateData.membershipLevel !== undefined) {
      user.data.membershipLevel = updateData.membershipLevel;
    }

    const updatedUser = await this.userRepository.save(user.data);

    // Log activity
    await this.logActivity(
      ActivityType.USER_REGISTRATION, // Using closest match
      `User details updated by admin: ${updatedUser.displayName}`,
      updatedUser,
      admin,
      { changes: updateData },
    );

    return {
      success: true,
      data: updatedUser,
      message: 'User details updated successfully',
    };
  }

  async getInactiveUsers(admin: Admin) {
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.branch', 'branch')
      .where('user.isActive = :isActive', { isActive: false })
      .andWhere('user.role = :role', { role: UserRole.CUSTOMER });

    // Branch admin can only see users from their branch
    if (admin.role === AdminRole.BRANCH_ADMIN && admin.branch) {
      queryBuilder.andWhere('user.branchId = :branchId', {
        branchId: admin.branch.id,
      });
    }

    queryBuilder.orderBy('user.createdAt', 'DESC');

    const users = await queryBuilder.getMany();

    return {
      success: true,
      data: users,
    };
  }

  async bulkActivateUsers(userIds: number[], admin: Admin, reason?: string) {
    const users = await this.userRepository.findByIds(userIds);

    if (users.length !== userIds.length) {
      throw new BadRequestException('Some users not found');
    }

    // Check permissions for branch admin
    if (admin.role === AdminRole.BRANCH_ADMIN && admin.branch) {
      const unauthorizedUsers = users.filter(
        (user) => user.branch?.id !== admin.branch!.id,
      );
      if (unauthorizedUsers.length > 0) {
        throw new ForbiddenException('Access denied to some users');
      }
    }

    const inactiveUsers = users.filter((user) => !user.isActive);

    if (inactiveUsers.length === 0) {
      throw new BadRequestException('No inactive users to activate');
    }

    // Activate users
    for (const user of inactiveUsers) {
      user.isActive = true;
    }

    const updatedUsers = await this.userRepository.save(inactiveUsers);

    // Log activity for each user
    for (const user of updatedUsers) {
      await this.logActivity(
        ActivityType.USER_REGISTRATION,
        `User bulk activated by admin: ${user.displayName}${reason ? `. Reason: ${reason}` : ''}`,
        user,
        admin,
        { reason, bulkOperation: true },
      );
    }

    return {
      success: true,
      data: updatedUsers,
      message: `${updatedUsers.length} users activated successfully`,
    };
  }

  private async logActivity(
    activityType: ActivityType,
    description: string,
    user?: User,
    admin?: Admin,
    metadata?: any,
  ): Promise<void> {
    const log = this.activityLogRepository.create({
      activityType,
      description,
      user,
      admin,
      metadata,
    });
    await this.activityLogRepository.save(log);
  }
}
