import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AdminRoles } from '../auth/decorators/admin-roles.decorator';
import { AdminRolesGuard } from '../auth/guards/admin-roles.guard';
import { GetAdmin } from '../auth/decorators/get-admin.decorator';
import { Admin, AdminRole } from '../entities/admin.entity';
import { AdminCourseService } from './admin-course.service';
import {
  CreateCourseDto,
  UpdateCourseDto,
  TransferSessionsDto,
  CourseQueryDto,
} from './dto/course.dto';

@Controller('admin/courses')
@UseGuards(AuthGuard('admin-jwt'), AdminRolesGuard)
export class AdminCourseController {
  constructor(private adminCourseService: AdminCourseService) {}

  @Get()
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async getAllCourses(
    @GetAdmin() admin: Admin,
    @Query() query: CourseQueryDto,
  ) {
    return this.adminCourseService.getAllCourses(admin, query);
  }

  @Get(':id')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async getCourseById(
    @Param('id', ParseIntPipe) id: number,
    @GetAdmin() admin: Admin,
  ) {
    return this.adminCourseService.getCourseById(id, admin);
  }

  @Get(':id/sessions')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async getCourseSessions(
    @Param('id', ParseIntPipe) id: number,
    @GetAdmin() admin: Admin,
  ) {
    return this.adminCourseService.getCourseSessions(id, admin);
  }

  @Post()
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async createCourse(
    @Body() createCourseDto: CreateCourseDto,
    @GetAdmin() admin: Admin,
  ) {
    return this.adminCourseService.createCourse(createCourseDto, admin);
  }

  @Put(':id')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async updateCourse(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCourseDto: UpdateCourseDto,
    @GetAdmin() admin: Admin,
  ) {
    return this.adminCourseService.updateCourse(id, updateCourseDto, admin);
  }

  @Post(':id/transfer-sessions')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async transferSessions(
    @Param('id', ParseIntPipe) fromCourseId: number,
    @Body() transferDto: TransferSessionsDto,
    @GetAdmin() admin: Admin,
  ) {
    return this.adminCourseService.transferSessions(
      fromCourseId,
      transferDto,
      admin,
    );
  }

  @Put(':id/extend-expiry')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async extendExpiry(
    @Param('id', ParseIntPipe) id: number,
    @Body('newExpiryDate') newExpiryDate: string,
    @GetAdmin() admin: Admin,
  ) {
    return this.adminCourseService.extendExpiry(
      id,
      new Date(newExpiryDate),
      admin,
    );
  }

  @Put(':id/add-sessions')
  @AdminRoles(AdminRole.SUPER_ADMIN, AdminRole.BRANCH_ADMIN)
  async addSessions(
    @Param('id', ParseIntPipe) id: number,
    @Body('additionalSessions') additionalSessions: number,
    @Body('reason') reason: string,
    @GetAdmin() admin: Admin,
  ) {
    return this.adminCourseService.addSessions(
      id,
      additionalSessions,
      reason,
      admin,
    );
  }

  @Delete(':id')
  @AdminRoles(AdminRole.SUPER_ADMIN)
  async deleteCourse(
    @Param('id', ParseIntPipe) id: number,
    @GetAdmin() admin: Admin,
  ) {
    return this.adminCourseService.deleteCourse(id, admin);
  }
}
