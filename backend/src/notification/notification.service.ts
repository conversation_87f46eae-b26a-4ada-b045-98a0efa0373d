import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import axios from 'axios';
import { User } from '../entities/user.entity';
import { Booking } from '../entities/booking.entity';
import { Course } from '../entities/course.entity';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);
  private emailTransporter;

  constructor(private configService: ConfigService) {
    this.initializeEmailTransporter();
  }

  private initializeEmailTransporter() {
    try {
      this.emailTransporter = nodemailer.createTransport({
        host: this.configService.get('SMTP_HOST'),
        port: this.configService.get('SMTP_PORT'),
        secure: false,
        auth: {
          user: this.configService.get('SMTP_USER'),
          pass: this.configService.get('SMTP_PASS'),
        },
      });
    } catch (error) {
      this.logger.error('Failed to initialize email transporter', error);
    }
  }

  async sendRegistrationEmail(user: User): Promise<void> {
    if (!this.emailTransporter || !user.email) return;

    try {
      const mailOptions = {
        from: this.configService.get('SMTP_USER'),
        to: user.email,
        subject: 'ยินดีต้อนรับสู่ BodyLabs Pilates',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2563eb;">ยินดีต้อนรับสู่ BodyLabs Pilates</h2>
            <p>สวัสดี ${user.displayName},</p>
            <p>ขอบคุณที่สมัครสมาชิกกับเรา! คุณสามารถเริ่มจองคลาส Pilates ได้แล้ว</p>
            <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3>ข้อมูลสมาชิก:</h3>
              <p><strong>ชื่อ:</strong> ${user.displayName}</p>
              <p><strong>ระดับสมาชิก:</strong> ${user.membershipLevel}</p>
              <p><strong>สาขา:</strong> ${user.branch?.name || 'ยังไม่ได้เลือก'}</p>
            </div>
            <p>หากมีคำถามใดๆ สามารถติดต่อเราได้ผ่าน LINE OA</p>
            <p>ขอบคุณครับ/ค่ะ<br>ทีมงาน BodyLabs Pilates</p>
          </div>
        `,
      };

      await this.emailTransporter.sendMail(mailOptions);
      this.logger.log(`Registration email sent to ${user.email}`);
    } catch (error) {
      this.logger.error('Failed to send registration email', error);
    }
  }

  async sendCoursePurchaseEmail(user: User, course: Course): Promise<void> {
    if (!this.emailTransporter || !user.email) return;

    try {
      const mailOptions = {
        from: this.configService.get('SMTP_USER'),
        to: user.email,
        subject: 'ยืนยันการซื้อคอร์ส - BodyLabs Pilates',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2563eb;">ยืนยันการซื้อคอร์ส</h2>
            <p>สวัสดี ${user.displayName},</p>
            <p>ขอบคุณที่ซื้อคอร์สกับเรา!</p>
            <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3>รายละเอียดคอร์ส:</h3>
              <p><strong>ชื่อคอร์ส:</strong> ${course.name}</p>
              <p><strong>จำนวนเซสชัน:</strong> ${course.totalSessions} เซสชัน</p>
              <p><strong>ราคา:</strong> ฿${course.price.toLocaleString()}</p>
              <p><strong>วันที่เริ่ม:</strong> ${new Date(course.startDate).toLocaleDateString('th-TH')}</p>
              <p><strong>วันหมดอายุ:</strong> ${new Date(course.expiryDate).toLocaleDateString('th-TH')}</p>
              <p><strong>สาขา:</strong> ${course.branch.name}</p>
            </div>
            <p>คุณสามารถเริ่มจองคลาสได้แล้วผ่านระบบ</p>
            <p>ขอบคุณครับ/ค่ะ<br>ทีมงาน BodyLabs Pilates</p>
          </div>
        `,
      };

      await this.emailTransporter.sendMail(mailOptions);
      this.logger.log(`Course purchase email sent to ${user.email}`);
    } catch (error) {
      this.logger.error('Failed to send course purchase email', error);
    }
  }

  async sendBookingConfirmationEmail(booking: Booking): Promise<void> {
    if (!this.emailTransporter || !booking.customer.email) return;

    try {
      const mailOptions = {
        from: this.configService.get('SMTP_USER'),
        to: booking.customer.email,
        subject: 'ยืนยันการจอง - BodyLabs Pilates',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2563eb;">ยืนยันการจอง</h2>
            <p>สวัสดี ${booking.customer.displayName},</p>
            <p>การจองของคุณได้รับการยืนยันแล้ว</p>
            <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3>รายละเอียดการจอง:</h3>
              <p><strong>คอร์ส:</strong> ${booking.course.name}</p>
              <p><strong>ครูผู้สอน:</strong> ${booking.trainer.displayName}</p>
              <p><strong>วันที่และเวลา:</strong> ${new Date(booking.scheduledDateTime).toLocaleString('th-TH')}</p>
              <p><strong>ระยะเวลา:</strong> ${booking.durationMinutes} นาที</p>
              <p><strong>สาขา:</strong> ${booking.branch.name}</p>
              <p><strong>ที่อยู่:</strong> ${booking.branch.address}</p>
              ${booking.notes ? `<p><strong>หมายเหตุ:</strong> ${booking.notes}</p>` : ''}
            </div>
            <p style="color: #dc2626;"><strong>โปรดมาถึงก่อนเวลา 15 นาที</strong></p>
            <p>หากต้องการยกเลิกการจอง กรุณาแจ้งล่วงหน้าอย่างน้อย 24 ชั่วโมง</p>
            <p>ขอบคุณครับ/ค่ะ<br>ทีมงาน BodyLabs Pilates</p>
          </div>
        `,
      };

      await this.emailTransporter.sendMail(mailOptions);
      this.logger.log(
        `Booking confirmation email sent to ${booking.customer.email}`,
      );
    } catch (error) {
      this.logger.error('Failed to send booking confirmation email', error);
    }
  }

  async sendSessionCompletionEmail(booking: Booking): Promise<void> {
    if (!this.emailTransporter || !booking.customer.email) return;

    try {
      // Generate trainer evaluation link
      const evaluationLink = `${this.configService.get('FRONTEND_URL')}/evaluate/${booking.id}`;

      const mailOptions = {
        from: this.configService.get('SMTP_USER'),
        to: booking.customer.email,
        subject: 'เซสชันเสร็จสิ้น - ประเมินครูผู้สอน',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2563eb;">เซสชันเสร็จสิ้น</h2>
            <p>สวัสดี ${booking.customer.displayName},</p>
            <p>เซสชันของคุณเสร็จสิ้นแล้ว ขอบคุณที่ใช้บริการ</p>
            <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3>รายละเอียดเซสชัน:</h3>
              <p><strong>คอร์ส:</strong> ${booking.course.name}</p>
              <p><strong>ครูผู้สอน:</strong> ${booking.trainer.displayName}</p>
              <p><strong>วันที่:</strong> ${new Date(booking.scheduledDateTime).toLocaleDateString('th-TH')}</p>
              ${booking.sessionNotes ? `<p><strong>บันทึกจากครู:</strong> ${booking.sessionNotes}</p>` : ''}
              ${booking.trainerRecommendations ? `<p><strong>คำแนะนำ:</strong> ${booking.trainerRecommendations}</p>` : ''}
            </div>
            <div style="text-align: center; margin: 30px 0;">
              <a href="${evaluationLink}" style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                ประเมินครูผู้สอน
              </a>
            </div>
            <p>การประเมินของคุณจะช่วยให้เราปรับปรุงคุณภาพการสอนได้ดียิ่งขึ้น</p>
            <p>ขอบคุณครับ/ค่ะ<br>ทีมงาน BodyLabs Pilates</p>
          </div>
        `,
      };

      await this.emailTransporter.sendMail(mailOptions);
      this.logger.log(
        `Session completion email sent to ${booking.customer.email}`,
      );
    } catch (error) {
      this.logger.error('Failed to send session completion email', error);
    }
  }

  async sendLineMessage(userId: string, message: string): Promise<void> {
    try {
      const accessToken = this.configService.get('LINE_CHANNEL_ACCESS_TOKEN');
      if (!accessToken) return;

      await axios.post(
        'https://api.line.me/v2/bot/message/push',
        {
          to: userId,
          messages: [
            {
              type: 'text',
              text: message,
            },
          ],
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );

      this.logger.log(`LINE message sent to ${userId}`);
    } catch (error) {
      this.logger.error('Failed to send LINE message', error);
    }
  }

  async sendTelegramMessage(chatId: string, message: string): Promise<void> {
    try {
      const botToken = this.configService.get('TELEGRAM_BOT_TOKEN');
      if (!botToken) return;

      await axios.post(`https://api.telegram.org/bot${botToken}/sendMessage`, {
        chat_id: chatId,
        text: message,
        parse_mode: 'HTML',
      });

      this.logger.log(`Telegram message sent to ${chatId}`);
    } catch (error) {
      this.logger.error('Failed to send Telegram message', error);
    }
  }

  async notifyBookingCreated(booking: Booking): Promise<void> {
    // Send email notification
    await this.sendBookingConfirmationEmail(booking);

    // Send LINE notification to customer
    const lineMessage = `🎯 การจองสำเร็จ!\n\nคอร์ส: ${booking.course.name}\nครู: ${booking.trainer.displayName}\nวันที่: ${new Date(booking.scheduledDateTime).toLocaleString('th-TH')}\nสาขา: ${booking.branch.name}\n\nโปรดมาถึงก่อนเวลา 15 นาที`;
    await this.sendLineMessage(booking.customer.lineUserId, lineMessage);
  }

  async notifySessionCompleted(booking: Booking): Promise<void> {
    // Send completion email with evaluation link
    await this.sendSessionCompletionEmail(booking);

    // Send LINE notification
    const lineMessage = `✅ เซสชันเสร็จสิ้น!\n\nขอบคุณที่ใช้บริการ ${booking.course.name}\nครู: ${booking.trainer.displayName}\n\nกรุณาประเมินครูผู้สอนเพื่อช่วยปรับปรุงคุณภาพการสอน`;
    await this.sendLineMessage(booking.customer.lineUserId, lineMessage);
  }
}
