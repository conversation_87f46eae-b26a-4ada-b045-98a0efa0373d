import { Command, CommandRunner, Option } from 'nest-commander';
import { Injectable } from '@nestjs/common';
import { AdminAuthService } from '../auth/admin-auth.service';
import { AdminRole } from '../entities/admin.entity';

interface CreateAdminOptions {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: AdminRole;
  phoneNumber?: string;
}

@Injectable()
@Command({
  name: 'create-admin',
  description: 'Create a new admin user',
})
export class CreateAdminCommand extends CommandRunner {
  constructor(private adminAuthService: AdminAuthService) {
    super();
  }

  async run(passedParams: string[], options: CreateAdminOptions): Promise<void> {
    try {
      const { email, password, firstName, lastName, role = AdminRole.SUPER_ADMIN, phoneNumber } = options;

      if (!email || !password || !firstName || !lastName) {
        console.error('❌ Email, password, firstName, and lastName are required');
        process.exit(1);
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        console.error('❌ Invalid email format');
        process.exit(1);
      }

      // Validate password strength
      if (password.length < 8) {
        console.error('❌ Password must be at least 8 characters long');
        process.exit(1);
      }

      const admin = await this.adminAuthService.createAdmin({
        email,
        password,
        firstName,
        lastName,
        role,
        phoneNumber,
      });

      console.log('✅ Admin user created successfully!');
      console.log(`📧 Email: ${admin.email}`);
      console.log(`👤 Name: ${admin.fullName}`);
      console.log(`🔑 Role: ${admin.role}`);
      console.log(`📱 Phone: ${admin.phoneNumber || 'Not provided'}`);
      console.log(`🆔 ID: ${admin.id}`);
      console.log('\n🚀 You can now login to the admin panel with these credentials.');
    } catch (error) {
      console.error('❌ Failed to create admin user:', error.message);
      process.exit(1);
    }
  }

  @Option({
    flags: '-e, --email <email>',
    description: 'Admin email address',
    required: true,
  })
  parseEmail(val: string): string {
    return val;
  }

  @Option({
    flags: '-p, --password <password>',
    description: 'Admin password (min 8 characters)',
    required: true,
  })
  parsePassword(val: string): string {
    return val;
  }

  @Option({
    flags: '-f, --firstName <firstName>',
    description: 'Admin first name',
    required: true,
  })
  parseFirstName(val: string): string {
    return val;
  }

  @Option({
    flags: '-l, --lastName <lastName>',
    description: 'Admin last name',
    required: true,
  })
  parseLastName(val: string): string {
    return val;
  }

  @Option({
    flags: '-r, --role <role>',
    description: 'Admin role (super_admin or branch_admin)',
    defaultValue: AdminRole.SUPER_ADMIN,
  })
  parseRole(val: string): AdminRole {
    if (val === 'super_admin' || val === 'branch_admin') {
      return val as AdminRole;
    }
    throw new Error('Role must be either "super_admin" or "branch_admin"');
  }

  @Option({
    flags: '--phoneNumber <phoneNumber>',
    description: 'Admin phone number (optional)',
  })
  parsePhoneNumber(val: string): string {
    return val;
  }
}
