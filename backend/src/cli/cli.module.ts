import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { CreateAdminCommand } from './create-admin.command';
import { AdminAuthService } from '../auth/admin-auth.service';
import { Admin } from '../entities/admin.entity';
import { ActivityLog } from '../entities/activity-log.entity';
import { JwtModule } from '@nestjs/jwt';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forFeature([Admin, ActivityLog]),
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'default-secret',
      signOptions: { expiresIn: '7d' },
    }),
  ],
  providers: [CreateAdminCommand, AdminAuthService],
})
export class CliModule {}
