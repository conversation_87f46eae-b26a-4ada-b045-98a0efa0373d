import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-custom';
import { AuthService } from '../auth.service';

@Injectable()
export class LineStrategy extends PassportStrategy(Strategy, 'line') {
  constructor(private authService: AuthService) {
    super();
  }

  async validate(req: any): Promise<any> {
    const { accessToken } = req.body;
    if (!accessToken) {
      return false;
    }

    try {
      const lineProfile = await this.authService.validateLineToken(accessToken);
      return lineProfile;
    } catch (error) {
      return false;
    }
  }
}
