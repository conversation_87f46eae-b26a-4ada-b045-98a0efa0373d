import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import {
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsEnum,
  Min,
  MinLength,
} from 'class-validator';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { Admin, AdminRole } from '../entities/admin.entity';
import { ActivityLog, ActivityType } from '../entities/activity-log.entity';
import { Transform } from 'stream';

export interface AdminJwtPayload {
  sub: number;
  email: string;
  role: AdminRole;
  type: 'admin';
  iat?: number;
  exp?: number;
}

export class AdminLoginDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @IsOptional()
  @IsNumber()
  @Min(1)
  branchId?: number;
}

export class CreateAdminDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  @IsNotEmpty()
  lastName: string;

  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @IsEnum(AdminRole)
  role: AdminRole;

  @IsOptional()
  @IsNumber()
  @Min(1)
  branchId?: number;
}

export class UpdateAdminDto {
  @IsOptional()
  @IsEmail()
  @IsNotEmpty()
  email?: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password?: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  firstName?: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  lastName?: string;

  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @IsOptional()
  @IsEnum(AdminRole)
  role?: AdminRole;

  @IsOptional()
  @IsNumber()
  @Min(1)
  branchId?: number;
}

@Injectable()
export class AdminAuthService {
  constructor(
    @InjectRepository(Admin)
    private adminRepository: Repository<Admin>,
    @InjectRepository(ActivityLog)
    private activityLogRepository: Repository<ActivityLog>,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async loginAdmin(
    loginDto: AdminLoginDto,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<{ admin: Admin; token: string }> {
    const { email, password, branchId } = loginDto;

    // Find admin by email
    const admin = await this.adminRepository.findOne({
      where: { email: email.toLowerCase() },
      relations: ['branch'],
    });

    if (!admin) {
      throw new UnauthorizedException('Invalid email or password');
    }

    if (!admin.isActive) {
      throw new UnauthorizedException('Admin account is deactivated');
    }

    // Validate password
    const isPasswordValid = await admin.validatePassword(password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid email or password');
    }

    // For branch admin, validate branch access
    if (admin.role === AdminRole.BRANCH_ADMIN) {
      if (!branchId) {
        throw new BadRequestException(
          'Branch selection is required for branch admin login',
        );
      }

      // Check if admin has access to the selected branch
      if (!admin.branch || admin.branch.id !== branchId) {
        throw new UnauthorizedException(
          'You do not have access to the selected branch',
        );
      }
    }

    // Update last login
    admin.lastLoginAt = new Date();
    await this.adminRepository.save(admin);

    // Create JWT payload
    const payload: AdminJwtPayload = {
      sub: admin.id,
      email: admin.email,
      role: admin.role,
      type: 'admin',
    };

    const token = this.jwtService.sign(payload);

    // Log login activity
    await this.logActivity(
      ActivityType.LOGIN,
      `Admin logged in: ${admin.fullName}`,
      undefined,
      admin,
      { email: admin.email },
      ipAddress,
      userAgent,
    );

    return { admin, token };
  }

  async validateAdmin(payload: AdminJwtPayload): Promise<Admin> {
    if (payload.type !== 'admin') {
      throw new UnauthorizedException('Invalid token type');
    }

    const admin = await this.adminRepository.findOne({
      where: { id: payload.sub },
      relations: ['branch'],
    });

    if (!admin || !admin.isActive) {
      throw new UnauthorizedException('Admin not found or inactive');
    }

    return admin;
  }

  async createAdmin(createAdminDto: CreateAdminDto): Promise<Admin> {
    const { email, branchId, ...adminData } = createAdminDto;

    // Check if admin already exists
    const existingAdmin = await this.adminRepository.findOne({
      where: { email: email.toLowerCase() },
    });

    if (existingAdmin) {
      throw new BadRequestException('Admin with this email already exists');
    }

    // Create admin
    const admin = this.adminRepository.create({
      ...adminData,
      email: email.toLowerCase(),
      branch: branchId ? { id: branchId } : undefined,
    });

    const savedAdmin: Admin = await this.adminRepository.save(admin);

    // Log creation activity
    await this.logActivity(
      ActivityType.USER_REGISTRATION,
      `New admin created: ${savedAdmin.fullName}`,
      undefined,
      savedAdmin,
      { email: savedAdmin.email, role: savedAdmin.role },
    );

    const result = await this.adminRepository.findOne({
      where: { id: savedAdmin.id },
      relations: ['branch'],
    });

    if (!result) {
      throw new BadRequestException('Failed to retrieve created admin');
    }

    return result;
  }

  async getAllAdmins(): Promise<Admin[]> {
    return this.adminRepository.find({
      relations: ['branch'],
      order: { createdAt: 'DESC' },
    });
  }

  async getAdminById(id: number): Promise<Admin> {
    const admin = await this.adminRepository.findOne({
      where: { id },
      relations: ['branch'],
    });

    if (!admin) {
      throw new UnauthorizedException('Admin not found');
    }

    return admin;
  }

  async updateAdmin(id: number, updateData: UpdateAdminDto): Promise<Admin> {
    const admin = await this.getAdminById(id);

    if (updateData.email) {
      updateData.email = updateData.email.toLowerCase();

      // Check if email is already taken by another admin
      const existingAdmin = await this.adminRepository.findOne({
        where: { email: updateData.email },
      });

      if (existingAdmin && existingAdmin.id !== id) {
        throw new BadRequestException('Email already taken by another admin');
      }
    }

    Object.assign(admin, updateData);
    return this.adminRepository.save(admin);
  }

  async deactivateAdmin(id: number): Promise<Admin> {
    const admin = await this.getAdminById(id);
    admin.isActive = false;
    return this.adminRepository.save(admin);
  }

  async changePassword(id: number, newPassword: string): Promise<void> {
    const admin = await this.getAdminById(id);
    admin.password = newPassword; // Will be hashed by BeforeUpdate hook
    await this.adminRepository.save(admin);

    // Log password change
    await this.logActivity(
      ActivityType.USER_REGISTRATION, // Using this as closest match
      `Admin password changed: ${admin.fullName}`,
      undefined,
      admin,
      { email: admin.email },
    );
  }

  private async logActivity(
    activityType: ActivityType,
    description: string,
    user?: any,
    admin?: Admin,
    metadata?: any,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<void> {
    const log = this.activityLogRepository.create({
      activityType,
      description,
      user,
      admin,
      metadata,
      ipAddress,
      userAgent,
    });
    await this.activityLogRepository.save(log);
  }
}
