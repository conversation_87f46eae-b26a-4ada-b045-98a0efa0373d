import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { User, UserRole } from '../entities/user.entity';
import { ActivityLog, ActivityType } from '../entities/activity-log.entity';

export interface LineProfile {
  userId: string;
  displayName: string;
  pictureUrl?: string;
  statusMessage?: string;
}

export interface JwtPayload {
  sub: number;
  lineUserId: string;
  role: UserRole;
  iat?: number;
  exp?: number;
}

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(ActivityLog)
    private activityLogRepository: Repository<ActivityLog>,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async validateLineToken(accessToken: string): Promise<LineProfile> {
    try {
      const response = await axios.get('https://api.line.me/v2/profile', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return response.data;
    } catch (error) {
      throw new UnauthorizedException('Invalid LINE access token');
    }
  }

  async loginWithLine(
    accessToken: string,
    ipAddress?: string,
    userAgent?: string,
    email?: string,
  ): Promise<{ user: User; token: string }> {
    const lineProfile = await this.validateLineToken(accessToken);

    let user = await this.userRepository.findOne({
      where: { lineUserId: lineProfile.userId },
      relations: ['branch'],
    });

    if (!user) {
      // Create new user as INACTIVE by default
      user = this.userRepository.create({
        lineUserId: lineProfile.userId,
        displayName: lineProfile.displayName,
        profilePictureUrl: lineProfile.pictureUrl,
        email: email || undefined, // Use email from LIFF if provided
        role: UserRole.CUSTOMER,
        isActive: false, // Users start as inactive until admin activates them
      });
      user = await this.userRepository.save(user);

      // Log registration activity
      await this.logActivity(
        ActivityType.USER_REGISTRATION,
        `New user registered (inactive): ${user.displayName}`,
        user,
        { lineProfile },
        ipAddress,
        userAgent,
      );
    } else {
      // Update profile if needed
      let needsUpdate = false;

      if (user.displayName !== lineProfile.displayName) {
        user.displayName = lineProfile.displayName;
        needsUpdate = true;
      }

      if (user.profilePictureUrl !== lineProfile.pictureUrl) {
        user.profilePictureUrl = lineProfile.pictureUrl;
        needsUpdate = true;
      }

      // Update email if provided and user doesn't have one
      if (email && !user.email) {
        user.email = email;
        needsUpdate = true;
      }

      if (needsUpdate) {
        await this.userRepository.save(user);
      }

      // Log login activity
      await this.logActivity(
        ActivityType.LOGIN,
        `User logged in: ${user.displayName}`,
        user,
        {},
        ipAddress,
        userAgent,
      );
    }

    // Allow inactive users to authenticate but they'll have limited access
    // The frontend will handle showing appropriate messages for inactive users

    const payload: JwtPayload = {
      sub: user.id,
      lineUserId: user.lineUserId,
      role: user.role,
    };

    const token = this.jwtService.sign(payload);

    return { user, token };
  }

  async validateUser(payload: JwtPayload): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id: payload.sub },
      relations: ['branch'],
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Allow inactive users to authenticate but they'll have limited functionality
    return user;
  }

  private async logActivity(
    activityType: ActivityType,
    description: string,
    user?: User,
    metadata?: any,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<void> {
    const log = this.activityLogRepository.create({
      activityType,
      description,
      user,
      metadata,
      ipAddress,
      userAgent,
    });
    await this.activityLogRepository.save(log);
  }
}
