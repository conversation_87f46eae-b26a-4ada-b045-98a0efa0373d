import { Controller, Post, Body, Req, UseGuards, Get } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';
import { AuthService } from './auth.service';
import { User } from '../entities/user.entity';
import { GetUser } from './decorators/get-user.decorator';

export class LineLoginDto {
  @IsString()
  @IsNotEmpty()
  accessToken: string;

  @IsString()
  @IsOptional()
  email?: string;
}

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('line/login')
  async lineLogin(@Body() lineLoginDto: LineLoginDto, @Req() req: Request) {
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent');

    const result = await this.authService.loginWithLine(
      lineLoginDto.accessToken,
      ipAddress,
      userAgent,
      lineLoginDto.email,
    );

    return {
      success: true,
      data: {
        user: {
          id: result.user.id,
          lineUserId: result.user.lineUserId,
          displayName: result.user.displayName,
          email: result.user.email,
          phoneNumber: result.user.phoneNumber,
          profilePictureUrl: result.user.profilePictureUrl,
          role: result.user.role,
          membershipLevel: result.user.membershipLevel,
          hasGivenConsent: result.user.hasGivenConsent,
          branch: result.user.branch,
        },
        token: result.token,
      },
    };
  }

  @Get('profile')
  @UseGuards(AuthGuard('jwt'))
  async getProfile(@GetUser() user: User) {
    return {
      success: true,
      data: {
        id: user.id,
        lineUserId: user.lineUserId,
        displayName: user.displayName,
        email: user.email,
        phoneNumber: user.phoneNumber,
        profilePictureUrl: user.profilePictureUrl,
        role: user.role,
        membershipLevel: user.membershipLevel,
        totalSpent: user.totalSpent,
        hasGivenConsent: user.hasGivenConsent,
        branch: user.branch,
      },
    };
  }

  @Post('logout')
  @UseGuards(AuthGuard('jwt'))
  async logout(@GetUser() user: User, @Req() req: Request) {
    // In a real application, you might want to blacklist the token
    // For now, we'll just log the logout activity
    return {
      success: true,
      message: 'Logged out successfully',
    };
  }
}
