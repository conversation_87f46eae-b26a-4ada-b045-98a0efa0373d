import {
  <PERSON>,
  Post,
  Get,
  Put,
  Body,
  Param,
  Req,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';
import {
  AdminAuthService,
  AdminLoginDto,
  CreateAdminDto,
  UpdateAdminDto,
} from './admin-auth.service';
import { Admin, AdminRole } from '../entities/admin.entity';
import { GetAdmin } from './decorators/get-admin.decorator';
import { AdminRoles } from './decorators/admin-roles.decorator';
import { AdminRolesGuard } from './guards/admin-roles.guard';

@Controller('admin/auth')
export class AdminAuthController {
  constructor(private adminAuthService: AdminAuthService) {}

  @Post('login')
  async adminLogin(@Body() loginDto: AdminLoginDto, @Req() req: Request) {
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent');

    const result = await this.adminAuthService.loginAdmin(
      loginDto,
      ipAddress,
      userAgent,
    );

    return {
      success: true,
      data: {
        admin: {
          id: result.admin.id,
          email: result.admin.email,
          firstName: result.admin.firstName,
          lastName: result.admin.lastName,
          fullName: result.admin.fullName,
          phoneNumber: result.admin.phoneNumber,
          role: result.admin.role,
          isActive: result.admin.isActive,
          lastLoginAt: result.admin.lastLoginAt,
          branch: result.admin.branch,
        },
        token: result.token,
      },
    };
  }

  @Get('profile')
  @UseGuards(AuthGuard('admin-jwt'))
  async getAdminProfile(@GetAdmin() admin: Admin) {
    return {
      success: true,
      data: {
        id: admin.id,
        email: admin.email,
        firstName: admin.firstName,
        lastName: admin.lastName,
        fullName: admin.fullName,
        phoneNumber: admin.phoneNumber,
        role: admin.role,
        isActive: admin.isActive,
        lastLoginAt: admin.lastLoginAt,
        branch: admin.branch,
      },
    };
  }

  @Post('create')
  @UseGuards(AuthGuard('admin-jwt'), AdminRolesGuard)
  @AdminRoles(AdminRole.SUPER_ADMIN)
  async createAdmin(@Body() createAdminDto: CreateAdminDto) {
    const admin = await this.adminAuthService.createAdmin(createAdminDto);
    return {
      success: true,
      data: admin,
      message: 'Admin created successfully',
    };
  }

  @Get('admins')
  @UseGuards(AuthGuard('admin-jwt'), AdminRolesGuard)
  @AdminRoles(AdminRole.SUPER_ADMIN)
  async getAllAdmins() {
    const admins = await this.adminAuthService.getAllAdmins();
    return {
      success: true,
      data: admins,
    };
  }

  @Get('admins/:id')
  @UseGuards(AuthGuard('admin-jwt'), AdminRolesGuard)
  @AdminRoles(AdminRole.SUPER_ADMIN)
  async getAdminById(@Param('id', ParseIntPipe) id: number) {
    const admin = await this.adminAuthService.getAdminById(id);
    return {
      success: true,
      data: admin,
    };
  }

  @Put('admins/:id')
  @UseGuards(AuthGuard('admin-jwt'), AdminRolesGuard)
  @AdminRoles(AdminRole.SUPER_ADMIN)
  async updateAdmin(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateData: UpdateAdminDto,
  ) {
    const admin = await this.adminAuthService.updateAdmin(id, updateData);
    return {
      success: true,
      data: admin,
      message: 'Admin updated successfully',
    };
  }

  @Put('admins/:id/deactivate')
  @UseGuards(AuthGuard('admin-jwt'), AdminRolesGuard)
  @AdminRoles(AdminRole.SUPER_ADMIN)
  async deactivateAdmin(@Param('id', ParseIntPipe) id: number) {
    const admin = await this.adminAuthService.deactivateAdmin(id);
    return {
      success: true,
      data: admin,
      message: 'Admin deactivated successfully',
    };
  }

  @Put('change-password')
  @UseGuards(AuthGuard('admin-jwt'))
  async changePassword(
    @GetAdmin() admin: Admin,
    @Body('newPassword') newPassword: string,
  ) {
    await this.adminAuthService.changePassword(admin.id, newPassword);
    return {
      success: true,
      message: 'Password changed successfully',
    };
  }

  @Post('logout')
  @UseGuards(AuthGuard('admin-jwt'))
  async logout(@GetAdmin() admin: Admin) {
    // In a real application, you might want to blacklist the token
    return {
      success: true,
      message: 'Logged out successfully',
    };
  }
}
