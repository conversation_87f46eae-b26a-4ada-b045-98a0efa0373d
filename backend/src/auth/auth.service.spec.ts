import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { UnauthorizedException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { User, UserRole } from '../entities/user.entity';
import { ActivityLog } from '../entities/activity-log.entity';

describe('AuthService', () => {
  let service: AuthService;
  let userRepository: any;
  let activityLogRepository: any;
  let jwtService: JwtService;
  let configService: ConfigService;

  const mockUser = {
    id: '1',
    lineUserId: 'line123',
    displayName: 'Test User',
    role: UserRole.CUSTOMER,
    isActive: true,
  };

  const mockUserRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
  };

  const mockActivityLogRepository = {
    create: jest.fn(),
    save: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: getRepositoryToken(ActivityLog),
          useValue: mockActivityLogRepository,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userRepository = module.get(getRepositoryToken(User));
    activityLogRepository = module.get(getRepositoryToken(ActivityLog));
    jwtService = module.get<JwtService>(JwtService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validateUser', () => {
    it('should return user if found and active', async () => {
      const payload = { sub: '1', lineUserId: 'line123', role: UserRole.CUSTOMER };
      userRepository.findOne.mockResolvedValue(mockUser);

      const result = await service.validateUser(payload);

      expect(result).toEqual(mockUser);
      expect(userRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1' },
        relations: ['branch'],
      });
    });

    it('should throw UnauthorizedException if user not found', async () => {
      const payload = { sub: '1', lineUserId: 'line123', role: UserRole.CUSTOMER };
      userRepository.findOne.mockResolvedValue(null);

      await expect(service.validateUser(payload)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException if user is inactive', async () => {
      const payload = { sub: '1', lineUserId: 'line123', role: UserRole.CUSTOMER };
      const inactiveUser = { ...mockUser, isActive: false };
      userRepository.findOne.mockResolvedValue(inactiveUser);

      await expect(service.validateUser(payload)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('loginWithLine', () => {
    it('should create new user if not exists', async () => {
      const accessToken = 'valid-token';
      const lineProfile = {
        userId: 'line123',
        displayName: 'New User',
        pictureUrl: 'http://example.com/pic.jpg',
      };

      // Mock LINE API call
      jest.spyOn(service, 'validateLineToken').mockResolvedValue(lineProfile);
      
      userRepository.findOne.mockResolvedValue(null);
      userRepository.create.mockReturnValue(mockUser);
      userRepository.save.mockResolvedValue(mockUser);
      activityLogRepository.create.mockReturnValue({});
      activityLogRepository.save.mockResolvedValue({});
      mockJwtService.sign.mockReturnValue('jwt-token');

      const result = await service.loginWithLine(accessToken);

      expect(result.user).toEqual(mockUser);
      expect(result.token).toBe('jwt-token');
      expect(userRepository.create).toHaveBeenCalled();
      expect(userRepository.save).toHaveBeenCalled();
    });

    it('should return existing user if found', async () => {
      const accessToken = 'valid-token';
      const lineProfile = {
        userId: 'line123',
        displayName: 'Existing User',
        pictureUrl: 'http://example.com/pic.jpg',
      };

      jest.spyOn(service, 'validateLineToken').mockResolvedValue(lineProfile);
      
      userRepository.findOne.mockResolvedValue(mockUser);
      userRepository.save.mockResolvedValue(mockUser);
      activityLogRepository.create.mockReturnValue({});
      activityLogRepository.save.mockResolvedValue({});
      mockJwtService.sign.mockReturnValue('jwt-token');

      const result = await service.loginWithLine(accessToken);

      expect(result.user).toEqual(mockUser);
      expect(result.token).toBe('jwt-token');
      expect(userRepository.create).not.toHaveBeenCalled();
    });
  });
});
