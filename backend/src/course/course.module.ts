import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CourseController } from './course.controller';
import { CourseService } from './course.service';
import { Course } from '../entities/course.entity';
import { User } from '../entities/user.entity';
import { Branch } from '../entities/branch.entity';
import { ActivityLog } from '../entities/activity-log.entity';
import { AuthModule } from '../auth/auth.module';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Course, User, Branch, ActivityLog]),
    AuthModule,
    NotificationModule,
  ],
  controllers: [CourseController],
  providers: [CourseService],
  exports: [CourseService],
})
export class CourseModule {}
