import {
  Injectable,
  BadRequestException,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Course, CourseStatus } from '../entities/course.entity';
import { User, UserRole } from '../entities/user.entity';
import { Branch } from '../entities/branch.entity';
import { ActivityLog, ActivityType } from '../entities/activity-log.entity';
import { NotificationService } from '../notification/notification.service';

export class CreateCourseDto {
  name: string;
  description?: string;
  price: number;
  totalSessions: number;
  startDate: Date;
  expiryDate: Date;
  customerId: number;
  branchId: number;
  notes?: string;
}

export class UpdateCourseDto {
  name?: string;
  description?: string;
  totalSessions?: number;
  expiryDate?: Date;
  status?: CourseStatus;
  notes?: string;
}

export class TransferSessionsDto {
  fromCourseId: number;
  toCourseId: number;
  sessionsToTransfer: number;
}

@Injectable()
export class CourseService {
  constructor(
    @InjectRepository(Course)
    private courseRepository: Repository<Course>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Branch)
    private branchRepository: Repository<Branch>,
    @InjectRepository(ActivityLog)
    private activityLogRepository: Repository<ActivityLog>,
    private notificationService: NotificationService,
  ) {}

  async createCourse(createCourseDto: CreateCourseDto): Promise<Course> {
    const {
      customerId,
      branchId,
      totalSessions,
      startDate,
      expiryDate,
      ...courseData
    } = createCourseDto;

    // Validate customer
    const customer = await this.userRepository.findOne({
      where: { id: customerId, role: UserRole.CUSTOMER, isActive: true },
    });

    if (!customer) {
      throw new NotFoundException('Customer not found or inactive');
    }

    // Validate branch
    const branch = await this.branchRepository.findOne({
      where: { id: branchId, isActive: true },
    });

    if (!branch) {
      throw new NotFoundException('Branch not found or inactive');
    }

    // Validate dates
    if (new Date(startDate) >= new Date(expiryDate)) {
      throw new BadRequestException('Start date must be before expiry date');
    }

    // Create course
    const course = this.courseRepository.create({
      ...courseData,
      totalSessions,
      remainingSessions: totalSessions,
      usedSessions: 0,
      startDate: new Date(startDate),
      expiryDate: new Date(expiryDate),
      customer,
      branch,
      status: CourseStatus.ACTIVE,
    });

    const savedCourse = await this.courseRepository.save(course);

    // Update customer total spent
    customer.totalSpent =
      Number(customer.totalSpent) + Number(courseData.price);
    await this.userRepository.save(customer);

    // Log activity
    await this.logActivity(
      ActivityType.COURSE_PURCHASE,
      `Course purchased: ${courseData.name} for ${customer.displayName}`,
      customer,
      { courseId: savedCourse.id, price: courseData.price, totalSessions },
    );

    const result = await this.courseRepository.findOne({
      where: { id: savedCourse.id },
      relations: ['customer', 'branch'],
    });

    if (!result) {
      throw new NotFoundException('Course not found after creation');
    }

    // Send course purchase notifications
    try {
      await this.notificationService.sendCoursePurchaseEmail(customer, result);
    } catch (error) {
      console.error('Failed to send course purchase notifications:', error);
      // Don't fail the course creation if notifications fail
    }

    return result;
  }

  async updateCourse(
    courseId: number,
    updateCourseDto: UpdateCourseDto,
    userId: number,
    userRole: UserRole,
  ): Promise<Course> {
    const course = await this.courseRepository.findOne({
      where: { id: courseId },
      relations: ['customer', 'branch'],
    });

    if (!course) {
      throw new NotFoundException('Course not found');
    }

    // Check permissions - only branch admin can modify courses
    if (
      userRole !== UserRole.BRANCH_ADMIN &&
      userRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException('Only branch admin can modify courses');
    }

    const { totalSessions, ...otherUpdates } = updateCourseDto;

    // Handle session count changes
    if (totalSessions !== undefined) {
      const sessionDifference = totalSessions - course.totalSessions;
      course.totalSessions = totalSessions;
      course.remainingSessions = Math.max(
        0,
        course.remainingSessions + sessionDifference,
      );
    }

    // Apply other updates
    Object.assign(course, otherUpdates);

    const updatedCourse = await this.courseRepository.save(course);

    // Log activity
    await this.logActivity(
      ActivityType.COURSE_MODIFIED,
      `Course modified: ${course.name} for ${course.customer.displayName}`,
      course.customer,
      { courseId, changes: updateCourseDto },
    );

    return updatedCourse;
  }

  async transferSessions(
    transferDto: TransferSessionsDto,
    userId: number,
    userRole: UserRole,
  ): Promise<{ fromCourse: Course; toCourse: Course }> {
    const { fromCourseId, toCourseId, sessionsToTransfer } = transferDto;

    // Check permissions
    if (
      userRole !== UserRole.BRANCH_ADMIN &&
      userRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException('Only branch admin can transfer sessions');
    }

    // Get both courses
    const fromCourse = await this.courseRepository.findOne({
      where: { id: fromCourseId },
      relations: ['customer', 'branch'],
    });

    const toCourse = await this.courseRepository.findOne({
      where: { id: toCourseId },
      relations: ['customer', 'branch'],
    });

    if (!fromCourse || !toCourse) {
      throw new NotFoundException('One or both courses not found');
    }

    // Validate transfer
    if (fromCourse.customer.id !== toCourse.customer.id) {
      throw new BadRequestException('Courses must belong to the same customer');
    }

    if (fromCourse.remainingSessions < sessionsToTransfer) {
      throw new BadRequestException(
        'Not enough remaining sessions to transfer',
      );
    }

    if (sessionsToTransfer <= 0) {
      throw new BadRequestException('Sessions to transfer must be positive');
    }

    // Perform transfer
    fromCourse.remainingSessions -= sessionsToTransfer;
    fromCourse.totalSessions -= sessionsToTransfer;

    toCourse.remainingSessions += sessionsToTransfer;
    toCourse.totalSessions += sessionsToTransfer;

    // Mark from course as transferred if no sessions left
    if (fromCourse.remainingSessions === 0) {
      fromCourse.status = CourseStatus.TRANSFERRED;
    }

    const updatedFromCourse = await this.courseRepository.save(fromCourse);
    const updatedToCourse = await this.courseRepository.save(toCourse);

    // Log activity
    await this.logActivity(
      ActivityType.COURSE_TRANSFERRED,
      `${sessionsToTransfer} sessions transferred from ${fromCourse.name} to ${toCourse.name}`,
      fromCourse.customer,
      { fromCourseId, toCourseId, sessionsToTransfer },
    );

    return {
      fromCourse: updatedFromCourse,
      toCourse: updatedToCourse,
    };
  }

  async getCourses(
    userId: number,
    userRole: UserRole,
    filters?: {
      customerId?: number;
      branchId?: number;
      status?: CourseStatus;
    },
  ): Promise<Course[]> {
    const queryBuilder = this.courseRepository
      .createQueryBuilder('course')
      .leftJoinAndSelect('course.customer', 'customer')
      .leftJoinAndSelect('course.branch', 'branch');

    // Apply role-based filtering
    if (userRole === UserRole.CUSTOMER) {
      queryBuilder.where('customer.id = :userId', { userId });
    }

    // Apply additional filters
    if (filters) {
      if (filters.customerId) {
        queryBuilder.andWhere('customer.id = :customerId', {
          customerId: filters.customerId,
        });
      }
      if (filters.branchId) {
        queryBuilder.andWhere('branch.id = :branchId', {
          branchId: filters.branchId,
        });
      }
      if (filters.status) {
        queryBuilder.andWhere('course.status = :status', {
          status: filters.status,
        });
      }
    }

    queryBuilder.orderBy('course.createdAt', 'DESC');

    return queryBuilder.getMany();
  }

  async getCourseById(
    courseId: number,
    userId: number,
    userRole: UserRole,
  ): Promise<Course> {
    const course = await this.courseRepository.findOne({
      where: { id: courseId },
      relations: ['customer', 'branch', 'bookings'],
    });

    if (!course) {
      throw new NotFoundException('Course not found');
    }

    // Check permissions
    if (userRole === UserRole.CUSTOMER && course.customer.id !== userId) {
      throw new ForbiddenException('You can only view your own courses');
    }

    return course;
  }

  private async logActivity(
    activityType: ActivityType,
    description: string,
    user?: User,
    metadata?: any,
  ): Promise<void> {
    const log = this.activityLogRepository.create({
      activityType,
      description,
      user,
      metadata,
    });
    await this.activityLogRepository.save(log);
  }
}
