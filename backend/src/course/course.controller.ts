import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import {
  CourseService,
  CreateCourseDto,
  UpdateCourseDto,
  TransferSessionsDto,
} from './course.service';
import { GetUser } from '../auth/decorators/get-user.decorator';
import { Roles } from '../auth/decorators/roles.decorator';
import { RolesGuard } from '../auth/guards/roles.guard';
import { User, UserRole } from '../entities/user.entity';
import { CourseStatus } from '../entities/course.entity';

@Controller('courses')
@UseGuards(AuthGuard('jwt'))
export class CourseController {
  constructor(private courseService: CourseService) {}

  @Post()
  @Roles(UserRole.BRANCH_ADMIN, UserRole.SUPER_ADMIN)
  @UseGuards(RolesGuard)
  async createCourse(@Body() createCourseDto: CreateCourseDto) {
    const course = await this.courseService.createCourse(createCourseDto);
    return {
      success: true,
      data: course,
      message: 'Course created successfully',
    };
  }

  @Get()
  async getCourses(
    @GetUser() user: User,
    @Query('customerId') customerId?: string,
    @Query('branchId') branchId?: string,
    @Query('status') status?: CourseStatus,
  ) {
    const filters = {
      customerId: customerId ? parseInt(customerId) : undefined,
      branchId: branchId ? parseInt(branchId) : undefined,
      status,
    };

    const courses = await this.courseService.getCourses(
      user.id,
      user.role,
      filters,
    );
    return {
      success: true,
      data: courses,
    };
  }

  @Get(':id')
  async getCourseById(
    @GetUser() user: User,
    @Param('id', ParseIntPipe) courseId: number,
  ) {
    const course = await this.courseService.getCourseById(
      courseId,
      user.id,
      user.role,
    );
    return {
      success: true,
      data: course,
    };
  }

  @Put(':id')
  @Roles(UserRole.BRANCH_ADMIN, UserRole.SUPER_ADMIN)
  @UseGuards(RolesGuard)
  async updateCourse(
    @GetUser() user: User,
    @Param('id', ParseIntPipe) courseId: number,
    @Body() updateCourseDto: UpdateCourseDto,
  ) {
    const course = await this.courseService.updateCourse(
      courseId,
      updateCourseDto,
      user.id,
      user.role,
    );
    return {
      success: true,
      data: course,
      message: 'Course updated successfully',
    };
  }

  @Post('transfer-sessions')
  @Roles(UserRole.BRANCH_ADMIN, UserRole.SUPER_ADMIN)
  @UseGuards(RolesGuard)
  async transferSessions(
    @GetUser() user: User,
    @Body() transferDto: TransferSessionsDto,
  ) {
    const result = await this.courseService.transferSessions(
      transferDto,
      user.id,
      user.role,
    );
    return {
      success: true,
      data: result,
      message: 'Sessions transferred successfully',
    };
  }
}
