import { CommandFactory } from 'nest-commander';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Module } from '@nestjs/common';
import { CliModule } from './cli/cli.module';
import { getDatabaseConfig } from './config/database.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: getDatabaseConfig,
      inject: [ConfigService],
    }),
    CliModule,
  ],
})
class AppCliModule {}

async function bootstrap() {
  await CommandFactory.run(AppCliModule, ['warn', 'error']);
}

bootstrap();
