import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { getDatabaseConfig } from './config/database.config';

// Modules
import { AuthModule } from './auth/auth.module';
import { BookingModule } from './booking/booking.module';
import { CourseModule } from './course/course.module';
import { CalendarModule } from './calendar/calendar.module';
import { NotificationModule } from './notification/notification.module';
import { AdminModule } from './admin/admin.module';

// Entities
import { User } from './entities/user.entity';
import { Admin } from './entities/admin.entity';
import { Branch } from './entities/branch.entity';
import { Course } from './entities/course.entity';
import { Booking } from './entities/booking.entity';
import { TrainerSchedule } from './entities/trainer-schedule.entity';
import { ActivityLog } from './entities/activity-log.entity';
import { UserBranchAccess } from './entities/user-branch-access.entity';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: getDatabaseConfig,
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      User,
      Admin,
      Branch,
      Course,
      Booking,
      TrainerSchedule,
      ActivityLog,
      UserBranchAccess,
    ]),
    ScheduleModule.forRoot(),
    AuthModule,
    BookingModule,
    CourseModule,
    CalendarModule,
    NotificationModule,
    AdminModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
