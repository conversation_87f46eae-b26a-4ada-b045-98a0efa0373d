import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { AdminAuthService } from '../src/auth/admin-auth.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Admin } from '../src/entities/admin.entity';
import { Repository } from 'typeorm';

async function checkAdmin() {
  let app;

  try {
    console.log('🔍 Checking Admin in Database\n');

    // Create NestJS application
    app = await NestFactory.createApplicationContext(AppModule);

    // Get the Admin repository
    const adminRepository = app.get(
      getRepositoryToken(Admin),
    ) as Repository<Admin>;

    // Find admin by email
    const admin = await adminRepository.findOne({
      where: { email: '<EMAIL>' },
    });

    if (!admin) {
      console.log('❌ No admin found with email: <EMAIL>');
      return;
    }

    console.log('✅ Admin found:');
    console.log(`📧 Email: ${admin.email}`);
    console.log(`👤 Name: ${admin.fullName}`);
    console.log(`🔑 Role: ${admin.role}`);
    console.log(`🔐 Password Hash: ${admin.password.substring(0, 20)}...`);
    console.log(`✅ Active: ${admin.isActive}`);
    console.log(`📅 Created: ${admin.createdAt}`);
    console.log(`📅 Last Login: ${admin.lastLoginAt || 'Never'}`);

    // Test password validation
    console.log('\n🧪 Testing password validation:');

    const testPasswords = ['P@SSw0rd', 'password', 'admin123', 'test123'];

    for (const testPassword of testPasswords) {
      const isValid = await admin.validatePassword(testPassword);
      console.log(
        `  "${testPassword}": ${isValid ? '✅ Valid' : '❌ Invalid'}`,
      );
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

// Load environment variables
import * as dotenv from 'dotenv';
dotenv.config();

checkAdmin();
