import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { AdminAuthService } from '../src/auth/admin-auth.service';
import * as readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query: string): Promise<string> {
  return new Promise(resolve => rl.question(query, resolve));
}

async function testAdminLogin() {
  let app;
  
  try {
    console.log('🧪 Testing Admin Login\n');

    // Get login credentials
    const email = await question('📧 Enter admin email: ');
    const password = await question('🔐 Enter admin password: ');

    if (!email || !password) {
      console.error('❌ Email and password are required');
      process.exit(1);
    }

    // Create NestJS application
    console.log('\n🔄 Initializing application...');
    app = await NestFactory.createApplicationContext(AppModule);
    
    // Get the AdminAuthService
    const adminAuthService = app.get(AdminAuthService);

    // Test login
    console.log('🔄 Testing login...');
    const result = await adminAuthService.loginAdmin({ email, password });

    console.log('\n✅ Login test successful!');
    console.log(`👤 Admin: ${result.admin.fullName}`);
    console.log(`📧 Email: ${result.admin.email}`);
    console.log(`🔑 Role: ${result.admin.role}`);
    console.log(`🎫 Token: ${result.token.substring(0, 20)}...`);
    console.log('\n🚀 Admin authentication is working correctly!');

  } catch (error) {
    console.error('❌ Login test failed:', error.message);
    
    if (error.message.includes('Invalid email or password')) {
      console.log('\n💡 Tip: Check your email and password');
    }
    
    process.exit(1);
  } finally {
    rl.close();
    if (app) {
      await app.close();
    }
  }
}

// Load environment variables
import * as dotenv from 'dotenv';
dotenv.config();

testAdminLogin();
