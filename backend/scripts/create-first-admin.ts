import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { AdminAuthService } from '../src/auth/admin-auth.service';
import { AdminRole } from '../src/entities/admin.entity';
import * as readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query: string): Promise<string> {
  return new Promise(resolve => rl.question(query, resolve));
}

async function createFirstAdmin() {
  let app;
  
  try {
    console.log('🚀 Creating First Admin User for BodyLabs Pilates\n');

    // Get admin details
    const email = await question('📧 Enter admin email: ');
    const password = await question('🔐 Enter admin password (min 8 characters): ');
    const firstName = await question('👤 Enter first name: ');
    const lastName = await question('👤 Enter last name: ');
    const phoneNumber = await question('📱 Enter phone number (optional): ');

    // Validate input
    if (!email || !password || !firstName || !lastName) {
      console.error('❌ Email, password, first name, and last name are required');
      process.exit(1);
    }

    if (password.length < 8) {
      console.error('❌ Password must be at least 8 characters long');
      process.exit(1);
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      console.error('❌ Invalid email format');
      process.exit(1);
    }

    // Create NestJS application
    console.log('\n🔄 Initializing application...');
    app = await NestFactory.createApplicationContext(AppModule);
    
    // Get the AdminAuthService
    const adminAuthService = app.get(AdminAuthService);

    // Create admin user
    console.log('🔄 Creating admin user...');
    const admin = await adminAuthService.createAdmin({
      email: email.toLowerCase(),
      password,
      firstName,
      lastName,
      phoneNumber: phoneNumber || undefined,
      role: AdminRole.SUPER_ADMIN,
    });

    console.log('\n✅ Admin user created successfully!');
    console.log(`📧 Email: ${admin.email}`);
    console.log(`👤 Name: ${admin.fullName}`);
    console.log(`🔑 Role: ${admin.role}`);
    console.log(`📱 Phone: ${admin.phoneNumber || 'Not provided'}`);
    console.log(`🆔 ID: ${admin.id}`);
    console.log('\n🚀 You can now login to the admin panel at: http://localhost:3000/admin/login');

  } catch (error) {
    console.error('❌ Failed to create admin user:', error.message);
    
    if (error.message.includes('already exists')) {
      console.log('\n💡 Tip: Try using a different email address');
    }
    
    process.exit(1);
  } finally {
    rl.close();
    if (app) {
      await app.close();
    }
  }
}

// Load environment variables
import * as dotenv from 'dotenv';
dotenv.config();

createFirstAdmin();
