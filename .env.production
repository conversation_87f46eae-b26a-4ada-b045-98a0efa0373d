# Production Environment Variables
# Copy this file to .env and fill in your actual values

# Database Configuration
DB_HOST=mysql
DB_PORT=3306
DB_USERNAME=bodylabs_user
DB_PASSWORD=your-secure-database-password
DB_DATABASE=bodylabs_pilates_prod

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-at-least-32-characters-long
JWT_EXPIRES_IN=7d

# LINE OA Configuration
LINE_CHANNEL_ID=your-line-channel-id
LINE_CHANNEL_SECRET=your-line-channel-secret
LINE_CHANNEL_ACCESS_TOKEN=your-line-channel-access-token

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Telegram Configuration
TELEGRAM_BOT_TOKEN=your-telegram-bot-token

# Google Calendar Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=https://your-domain.com/auth/google/callback

# Application Configuration
NODE_ENV=production
FRONTEND_URL=https://your-domain.com
NEXT_PUBLIC_API_URL=https://your-domain.com/api
NEXT_PUBLIC_LINE_LIFF_ID=your-liff-app-id
NEXT_PUBLIC_APP_URL=https://your-domain.com
